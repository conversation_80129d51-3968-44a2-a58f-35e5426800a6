import { useState } from 'react';
import {
  BookOpen,
  Clock,
  Calendar,
  Play,
  CheckCircle,
  Award
} from 'lucide-react';
import { Link } from 'react-router-dom';

const ActiveCourses = () => {
  const [selectedCourse, setSelectedCourse] = useState(null);

  const activeCourses = [
    {
      id: 1,
      title: 'Tabla Fundamentals',
      instructor: 'Guru <PERSON>',
      description: 'Master the basics of tabla playing with traditional techniques',
      progress: 75,
      totalLessons: 20,
      completedLessons: 15,
      nextClass: '2024-12-18 14:00',
      enrollmentDate: '2024-10-15',
      duration: '3 months',
      level: 'Beginner',
      category: 'Percussion',
      image: '/api/placeholder/400/250',
      modules: [
        { id: 1, title: 'Introduction to Tabla', lessons: 3, completed: 3, status: 'completed' },
        { id: 2, title: 'Basic Hand Positions', lessons: 4, completed: 4, status: 'completed' },
        { id: 3, title: 'Rhythm Patterns', lessons: 5, completed: 4, status: 'current' },
        { id: 4, title: 'Advanced Techniques', lessons: 8, completed: 0, status: 'locked' }
      ],
      recentActivity: [
        { type: 'lesson', title: 'Completed: Basic Rhythm Exercise', date: '2 days ago' },
        { type: 'assignment', title: 'Submitted: Hand Position Practice', date: '4 days ago' },
        { type: 'class', title: 'Attended: Live Practice Session', date: '1 week ago' }
      ],
      achievements: [
        { title: 'First Steps', description: 'Completed first lesson', earned: true },
        { title: 'Rhythm Master', description: 'Master 10 rhythm patterns', earned: false, progress: 7 }
      ]
    },
    {
      id: 2,
      title: 'Classical Vocal Training',
      instructor: 'Smt. Priya Sharma',
      description: 'Develop your classical vocal skills with traditional ragas',
      progress: 60,
      totalLessons: 24,
      completedLessons: 14,
      nextClass: '2024-12-19 16:30',
      enrollmentDate: '2024-09-20',
      duration: '4 months',
      level: 'Intermediate',
      category: 'Vocal',
      image: '/api/placeholder/400/250',
      modules: [
        { id: 1, title: 'Breathing Techniques', lessons: 4, completed: 4, status: 'completed' },
        { id: 2, title: 'Basic Ragas', lessons: 6, completed: 6, status: 'completed' },
        { id: 3, title: 'Voice Modulation', lessons: 8, completed: 4, status: 'current' },
        { id: 4, title: 'Advanced Compositions', lessons: 6, completed: 0, status: 'locked' }
      ],
      recentActivity: [
        { type: 'lesson', title: 'Completed: Raga Yaman Practice', date: '1 day ago' },
        { type: 'class', title: 'Attended: Group Vocal Session', date: '3 days ago' },
        { type: 'assignment', title: 'Submitted: Voice Recording', date: '5 days ago' }
      ],
      achievements: [
        { title: 'Vocal Foundation', description: 'Master breathing techniques', earned: true },
        { title: 'Raga Explorer', description: 'Learn 5 different ragas', earned: true }
      ]
    },
    {
      id: 3,
      title: 'Sitar Intermediate',
      instructor: 'Pt. Ravi Shankar',
      description: 'Advance your sitar skills with complex ragas and techniques',
      progress: 40,
      totalLessons: 18,
      completedLessons: 7,
      nextClass: '2024-12-20 15:00',
      enrollmentDate: '2024-11-01',
      duration: '5 months',
      level: 'Intermediate',
      category: 'String',
      image: '/api/placeholder/400/250',
      modules: [
        { id: 1, title: 'Sitar Setup & Tuning', lessons: 3, completed: 3, status: 'completed' },
        { id: 2, title: 'Finger Techniques', lessons: 5, completed: 4, status: 'current' },
        { id: 3, title: 'Raga Performance', lessons: 6, completed: 0, status: 'locked' },
        { id: 4, title: 'Improvisation', lessons: 4, completed: 0, status: 'locked' }
      ],
      recentActivity: [
        { type: 'lesson', title: 'Completed: Finger Exercise 3', date: '3 days ago' },
        { type: 'class', title: 'Attended: Sitar Tuning Workshop', date: '1 week ago' },
        { type: 'assignment', title: 'Submitted: Practice Recording', date: '1 week ago' }
      ],
      achievements: [
        { title: 'String Master', description: 'Perfect sitar tuning', earned: true },
        { title: 'Technique Builder', description: 'Master 15 finger techniques', earned: false, progress: 8 }
      ]
    }
  ];

  const getModuleStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'current': return 'text-blue-600 bg-blue-100';
      case 'locked': return 'text-gray-400 bg-gray-100';
      default: return 'text-gray-400 bg-gray-100';
    }
  };

  const getModuleStatusIcon = (status) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'current': return Play;
      case 'locked': return Clock;
      default: return Clock;
    }
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Active Courses</h1>
        <p className="text-gray-600 text-xl">Continue your learning journey with enrolled courses</p>
      </div>

      {/* Course Overview Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {activeCourses.map((course) => (
          <div key={course.id} className="dashboard-card group hover:shadow-lg transition-shadow">
            {/* Course Image */}
            <div className="aspect-video bg-gradient-to-br from-music-background-orange/20 to-music-background/20 rounded-t-xl flex items-center justify-center">
              <BookOpen size={48} className="text-music-background-orange" />
            </div>

            <div className="dashboard-card-content">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-1">{course.title}</h3>
                  <p className="text-sm text-gray-600">by {course.instructor}</p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  course.level === 'Beginner' ? 'text-green-600 bg-green-100' :
                  course.level === 'Intermediate' ? 'text-blue-600 bg-blue-100' :
                  'text-purple-600 bg-purple-100'
                }`}>
                  {course.level}
                </span>
              </div>

              <p className="text-sm text-gray-600 mb-4">{course.description}</p>

              {/* Progress */}
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-gray-600">Progress</span>
                  <span className="font-medium text-gray-800">{course.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-music-background-orange h-2 rounded-full transition-all duration-300"
                    style={{ width: `${course.progress}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {course.completedLessons} of {course.totalLessons} lessons completed
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center gap-2 text-gray-600">
                  <Calendar size={16} />
                  <span>Next: {new Date(course.nextClass).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Clock size={16} />
                  <span>{course.duration}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <button 
                  onClick={() => setSelectedCourse(selectedCourse === course.id ? null : course.id)}
                  className="flex-1 bg-music-background-orange hover:bg-music-background text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  View Details
                </button>
                <Link 
                  to="/student/classes"
                  className="px-3 py-2 border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors flex items-center justify-center"
                >
                  <Play size={16} />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Course View */}
      {selectedCourse && (
        <div className="dashboard-card mb-8">
          <div className="dashboard-card-content">
            {(() => {
              const course = activeCourses.find(c => c.id === selectedCourse);
              return (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-800">{course.title}</h2>
                    <button 
                      onClick={() => setSelectedCourse(null)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      ×
                    </button>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Course Modules */}
                    <div className="lg:col-span-2">
                      <h3 className="text-lg font-semibold text-gray-800 mb-4">Course Modules</h3>
                      <div className="space-y-3">
                        {course.modules.map((module) => {
                          const StatusIcon = getModuleStatusIcon(module.status);
                          return (
                            <div key={module.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getModuleStatusColor(module.status)}`}>
                                    <StatusIcon size={16} />
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-gray-800">{module.title}</h4>
                                    <p className="text-sm text-gray-600">
                                      {module.completed} of {module.lessons} lessons completed
                                    </p>
                                  </div>
                                </div>
                                {module.status === 'current' && (
                                  <button className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors">
                                    Continue
                                  </button>
                                )}
                              </div>
                              {module.status !== 'locked' && (
                                <div className="mt-3 ml-11">
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div 
                                      className="bg-music-background-orange h-1 rounded-full"
                                      style={{ width: `${(module.completed / module.lessons) * 100}%` }}
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                      {/* Recent Activity */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
                        <div className="space-y-3">
                          {course.recentActivity.map((activity, index) => (
                            <div key={index} className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-music-background-orange rounded-full mt-2" />
                              <div>
                                <p className="text-sm text-gray-800">{activity.title}</p>
                                <p className="text-xs text-gray-500">{activity.date}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Achievements */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Achievements</h3>
                        <div className="space-y-3">
                          {course.achievements.map((achievement, index) => (
                            <div key={index} className={`flex items-start gap-3 p-3 rounded-lg ${
                              achievement.earned ? 'bg-yellow-50 border border-yellow-200' : 'bg-gray-50 border border-gray-200'
                            }`}>
                              <Award size={16} className={achievement.earned ? 'text-yellow-600' : 'text-gray-400'} />
                              <div>
                                <p className={`text-sm font-medium ${achievement.earned ? 'text-yellow-800' : 'text-gray-600'}`}>
                                  {achievement.title}
                                </p>
                                <p className={`text-xs ${achievement.earned ? 'text-yellow-600' : 'text-gray-500'}`}>
                                  {achievement.description}
                                </p>
                                {!achievement.earned && achievement.progress && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    Progress: {achievement.progress}/10
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
};

export default ActiveCourses;
