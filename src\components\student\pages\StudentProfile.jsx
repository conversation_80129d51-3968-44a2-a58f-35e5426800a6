import { useState } from 'react';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  Edit,
  Save,
  X,
  CreditCard,
  Download,
  Eye,
  CheckCircle,
  Clock,
  Award,
  BookOpen
} from 'lucide-react';

const StudentProfile = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    firstName: 'Arjun',
    lastName: 'Patel',
    email: '<EMAIL>',
    phone: '+91 98765 43210',
    dateOfBirth: '1995-08-15',
    address: '123 Music Street, Mumbai, Maharashtra',
    emergencyContact: '+91 98765 43211',
    emergencyContactName: '<PERSON><PERSON>',
    enrollmentDate: '2024-10-15',
    studentId: 'SWR2024001'
  });

  const paymentHistory = [
    {
      id: 1,
      date: '2024-12-01',
      description: 'Monthly Subscription - December 2024',
      amount: 2999,
      status: 'completed',
      method: 'Credit Card',
      transactionId: 'TXN123456789',
      invoice: 'INV-2024-001'
    },
    {
      id: 2,
      date: '2024-11-01',
      description: 'Monthly Subscription - November 2024',
      amount: 2999,
      status: 'completed',
      method: 'UPI',
      transactionId: 'TXN123456788',
      invoice: 'INV-2024-002'
    },
    {
      id: 3,
      date: '2024-10-15',
      description: 'Course Enrollment - Tabla Fundamentals',
      amount: 3999,
      status: 'completed',
      method: 'Net Banking',
      transactionId: 'TXN123456787',
      invoice: 'INV-2024-003'
    },
    {
      id: 4,
      date: '2024-10-15',
      description: 'Course Enrollment - Classical Vocal Training',
      amount: 4999,
      status: 'completed',
      method: 'Credit Card',
      transactionId: 'TXN123456786',
      invoice: 'INV-2024-004'
    }
  ];

  const achievements = [
    {
      id: 1,
      title: 'First Steps',
      description: 'Completed your first lesson',
      earnedDate: '2024-10-16',
      icon: BookOpen,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      id: 2,
      title: 'Dedicated Learner',
      description: 'Attended 10 consecutive classes',
      earnedDate: '2024-11-15',
      icon: Calendar,
      color: 'text-green-600 bg-green-100'
    },
    {
      id: 3,
      title: 'Rhythm Master',
      description: 'Mastered 5 rhythm patterns',
      earnedDate: '2024-12-01',
      icon: Award,
      color: 'text-yellow-600 bg-yellow-100'
    }
  ];

  const learningStats = {
    totalCourses: 3,
    completedLessons: 42,
    totalPracticeHours: 156,
    averageScore: 87,
    currentStreak: 12,
    totalAssignments: 15,
    completedAssignments: 12
  };

  const handleSave = () => {
    // Here you would typically save to backend
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset form data
    setIsEditing(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Profile</h1>
        <p className="text-gray-600 text-xl">Manage your personal information and view account details</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6 w-fit">
        {[
          { key: 'profile', label: 'Personal Info' },
          { key: 'payments', label: 'Payment History' },
          { key: 'achievements', label: 'Achievements' },
          { key: 'stats', label: 'Learning Stats' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-white text-music-background-orange shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <div className="dashboard-card">
          <div className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-800">Personal Information</h2>
              {!isEditing ? (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-music-background-orange text-white rounded-lg hover:bg-music-background transition-colors"
                >
                  <Edit size={16} />
                  Edit Profile
                </button>
              ) : (
                <div className="flex gap-2">
                  <button
                    onClick={handleSave}
                    className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                  >
                    <Save size={16} />
                    Save
                  </button>
                  <button
                    onClick={handleCancel}
                    className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    <X size={16} />
                    Cancel
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="dashboard-card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Profile Picture */}
              <div className="md:col-span-2 flex items-center gap-6 mb-6">
                <div className="w-24 h-24 bg-music-background-orange rounded-full flex items-center justify-center">
                  <User size={48} className="text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-800">{profileData.firstName} {profileData.lastName}</h3>
                  <p className="text-gray-600">Student ID: {profileData.studentId}</p>
                  <p className="text-sm text-gray-500">Member since {formatDate(profileData.enrollmentDate)}</p>
                </div>
              </div>

              {/* Form Fields */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <input
                  type="text"
                  value={profileData.firstName}
                  onChange={(e) => setProfileData({...profileData, firstName: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input
                  type="text"
                  value={profileData.lastName}
                  onChange={(e) => setProfileData({...profileData, lastName: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <input
                  type="tel"
                  value={profileData.phone}
                  onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                <input
                  type="date"
                  value={profileData.dateOfBirth}
                  onChange={(e) => setProfileData({...profileData, dateOfBirth: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                <textarea
                  value={profileData.address}
                  onChange={(e) => setProfileData({...profileData, address: e.target.value})}
                  disabled={!isEditing}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Name</label>
                <input
                  type="text"
                  value={profileData.emergencyContactName}
                  onChange={(e) => setProfileData({...profileData, emergencyContactName: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Phone</label>
                <input
                  type="tel"
                  value={profileData.emergencyContact}
                  onChange={(e) => setProfileData({...profileData, emergencyContact: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent disabled:bg-gray-50"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment History Tab */}
      {activeTab === 'payments' && (
        <div className="dashboard-card">
          <div className="dashboard-card-header">
            <h2 className="text-xl font-semibold text-gray-800">Payment History</h2>
          </div>
          <div className="dashboard-card-content">
            <div className="space-y-4">
              {paymentHistory.map((payment) => (
                <div key={payment.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="font-medium text-gray-800">{payment.description}</h3>
                      <p className="text-sm text-gray-600">Transaction ID: {payment.transactionId}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-semibold text-gray-800">{formatCurrency(payment.amount)}</p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center gap-4">
                      <span>{formatDate(payment.date)}</span>
                      <span>via {payment.method}</span>
                    </div>
                    <div className="flex gap-2">
                      <button className="flex items-center gap-1 text-music-background-orange hover:text-music-background">
                        <Eye size={14} />
                        View
                      </button>
                      <button className="flex items-center gap-1 text-music-background-orange hover:text-music-background">
                        <Download size={14} />
                        Invoice
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Achievements Tab */}
      {activeTab === 'achievements' && (
        <div className="dashboard-card">
          <div className="dashboard-card-header">
            <h2 className="text-xl font-semibold text-gray-800">Achievements</h2>
          </div>
          <div className="dashboard-card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map((achievement) => {
                const Icon = achievement.icon;
                return (
                  <div key={achievement.id} className="border border-gray-200 rounded-lg p-4 text-center">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3 ${achievement.color}`}>
                      <Icon size={32} />
                    </div>
                    <h3 className="font-semibold text-gray-800 mb-1">{achievement.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                    <p className="text-xs text-gray-500">Earned on {formatDate(achievement.earnedDate)}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Learning Stats Tab */}
      {activeTab === 'stats' && (
        <div className="dashboard-card">
          <div className="dashboard-card-header">
            <h2 className="text-xl font-semibold text-gray-800">Learning Statistics</h2>
          </div>
          <div className="dashboard-card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-music-background-orange mb-2">{learningStats.totalCourses}</div>
                <p className="text-gray-600">Enrolled Courses</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">{learningStats.completedLessons}</div>
                <p className="text-gray-600">Completed Lessons</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">{learningStats.totalPracticeHours}h</div>
                <p className="text-gray-600">Practice Hours</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">{learningStats.averageScore}%</div>
                <p className="text-gray-600">Average Score</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-600 mb-2">{learningStats.currentStreak}</div>
                <p className="text-gray-600">Day Streak</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600 mb-2">{learningStats.completedAssignments}/{learningStats.totalAssignments}</div>
                <p className="text-gray-600">Assignments</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentProfile;
