// Demo data for student portal
export const studentData = {
  profile: {
    firstName: '<PERSON>r<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+91 98765 43210',
    studentId: 'SWR2024001',
    enrollmentDate: '2024-10-15'
  },
  
  stats: {
    enrolledCourses: 4,
    completedLessons: 28,
    practiceHours: 42,
    assignmentsDue: 3,
    currentStreak: 12,
    averageScore: 87
  },

  activeCourses: [
    {
      id: 1,
      title: 'Tabla Fundamentals',
      instructor: '<PERSON>',
      progress: 75,
      totalLessons: 20,
      completedLessons: 15,
      nextClass: '2024-12-18 14:00'
    },
    {
      id: 2,
      title: 'Classical Vocal Training',
      instructor: 'Smt. <PERSON><PERSON>',
      progress: 60,
      totalLessons: 24,
      completedLessons: 14,
      nextClass: '2024-12-19 16:30'
    },
    {
      id: 3,
      title: 'Sitar Intermediate',
      instructor: 'Pt. <PERSON>',
      progress: 40,
      totalLessons: 18,
      completedLessons: 7,
      nextClass: '2024-12-20 15:00'
    }
  ],

  upcomingClasses: [
    {
      id: 1,
      title: 'Tabla Practice Session',
      instructor: '<PERSON>',
      time: '2:00 PM - 3:00 PM',
      date: 'Today',
      canJoin: true
    },
    {
      id: 2,
      title: 'Vocal Technique Workshop',
      instructor: 'Smt. Priya <PERSON>',
      time: '4:30 PM - 5:30 PM',
      date: 'Today',
      canJoin: false
    }
  ],

  recentActivity: [
    {
      id: 1,
      type: 'assignment',
      title: 'Submitted Tabla Rhythm Assignment',
      time: '2 hours ago'
    },
    {
      id: 2,
      type: 'class',
      title: 'Attended Vocal Training Session',
      time: '1 day ago'
    },
    {
      id: 3,
      type: 'achievement',
      title: 'Completed Module 3 - Tabla Basics',
      time: '2 days ago'
    }
  ]
};

export const demoCredentials = {
  student: {
    email: '<EMAIL>',
    password: 'student123'
  },
  admin: {
    email: '<EMAIL>',
    password: 'admin123'
  }
};
