import Layout from "./layout/Layout"


const Payment = () => {
  const inputStyle = "w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-music-background focus:border-music-background text-gray-900 placeholder-gray-500 transition-colors"
  const labelStyle = "block text-sm font-medium text-white mb-2"
  return (
    <Layout>
      <div className="w-full text-white p-8">

        <h2 className="text-3xl font-semibold text-center">Payment</h2>
        <p className="text-center text-md mt-2 mb-4">Fill The Form To Proceed</p>
        <hr className="border-t border-white mb-6" />
        {/* Payment Form */}
        <form className="space-y-4">
          {/* First Name and Last Name */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={labelStyle}>First Name</label>
              <input
                type="text"
                className={inputStyle}
                placeholder=""
              />
            </div>
            <div>
              <label className={labelStyle}>Last Name</label>
              <input
                type="text"
                className={inputStyle}
                placeholder=""
              />
            </div>
          </div>

          {/* Course and Course Fee */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={labelStyle}>Course</label>
              <input
                type="text"
                className={inputStyle}
                placeholder="12 Months"
                value="12 Months"
                readOnly
              />
            </div>
            <div>
              <label className={labelStyle}>Course Fee</label>
              <input
                type="text"
                className={inputStyle}
                placeholder="3500 INR"
                value="3500 INR"
                readOnly
              />
            </div>
          </div>

          {/* Email and OTP */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className={labelStyle}>Email</label>
              <input
                type="email"
                className={inputStyle}
                placeholder="Your email address"
                value=""
              />
            </div>
            <div>
              <label className={labelStyle}>Enter OTP</label>
              <input
                type="text"
                className={inputStyle}
                placeholder="otp"
                value=""
               
                maxLength="6"
              />
            </div>
            <div>
              <label className={labelStyle}>&nbsp;</label>
              <button
                type="button"
                className="w-full py-2 px-4 bg-music-background hover:bg-music-background/90 text-white font-medium rounded-lg transition-colors"
              >
                Verify OTP
              </button>
            </div>

          </div>

          {/* Phone Number and OTP */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className={labelStyle}>Phone Number</label>
              <input
                type="tel"
                className={inputStyle}
                placeholder="Your Phone Number"
                value=""
                
              />
            </div>
            <div>
              <label className={labelStyle}>Enter OTP</label>
              <input
                type="text"
                className={inputStyle}
                placeholder="otp"
                value=""
              />
            </div>
            <div>
              <label className={labelStyle}>&nbsp;</label>
              <button
                type="button"
                className="w-full py-2 px-4 bg-music-background hover:bg-music-background/90 text-white font-medium rounded-lg transition-colors"
              >
                Verify OTP
              </button>
            </div>

          </div>

          {/* Terms and Conditions */}
          <div className="flex items-start space-x-3 mt-6">
            <input
              type="checkbox"
              id="terms"
              className="mt-1 h-4 w-4 text-white bg-transparent border-2 border-white rounded focus:ring-white focus:ring-2"
            />
            <label htmlFor="terms" className="text-sm text-white">
              I Agree Terms and conditions.
            </label>
          </div>
          
          {/* Payment Fee Button */}
          <div className="mt-8">
            <button
              type="submit"
              className="w-full py-2 px-6 bg-music-background hover:bg-music-background/90 text-white font-semibold rounded-lg transition-colors duration-200"
            >
              Payment Fee
            </button>
          </div>
        </form>
      </div>
    </Layout>
  )
}

export default Payment