
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Forget from './components/auth/Forget';
import Singin from './components/auth/Singin';
import DashboardLayout from './components/deshbord/DashboardLayout';
import Dashboard from './components/deshbord/pages/Dashboard';
import Courses from './components/deshbord/pages/Courses';
import Students from './components/deshbord/pages/Students';
import Schedule from './components/deshbord/pages/Schedule';
import ComingSoon from './components/deshbord/pages/ComingSoon';
import AddCourse from './components/deshbord/pages/AddCourse';
import PaymentHistory from './components/deshbord/pages/paymentHistory';
import Payment from './components/auth/Payment';
import Update_profile from './components/auth/Update_profile';

// Student Portal Components
import StudentDashboardLayout from './components/student/StudentDashboardLayout';
import StudentDashboard from './components/student/pages/StudentDashboard';
import Classes from './components/student/pages/Classes';
import ClassRecordings from './components/student/pages/ClassRecordings';
import ActiveCourses from './components/student/pages/ActiveCourses';
import ExploreCourses from './components/student/pages/ExploreCourses';
import Library from './components/student/pages/Library';
import Assignments from './components/student/pages/Assignments';
import StudentProfile from './components/student/pages/StudentProfile';

import './css/style.css';

function App() {
  return (
    <Router>
      <Routes>
        {/* Auth Routes */}
        <Route path="/signin" element={<Singin />} />
        <Route path="/forgot-password" element={<Forget />} />
        <Route path="/payment" element={<Payment />} />
        <Route path="/update-profile" element={<Update_profile />} />

        {/* Student Portal Routes */}
        <Route path="/student/dashboard" element={
          <StudentDashboardLayout>
            <StudentDashboard />
          </StudentDashboardLayout>
        } />
        <Route path="/student/classes" element={
          <StudentDashboardLayout>
            <Classes />
          </StudentDashboardLayout>
        } />
        <Route path="/student/recordings" element={
          <StudentDashboardLayout>
            <ClassRecordings />
          </StudentDashboardLayout>
        } />
        <Route path="/student/courses/active" element={
          <StudentDashboardLayout>
            <ActiveCourses />
          </StudentDashboardLayout>
        } />
        <Route path="/student/courses/explore" element={
          <StudentDashboardLayout>
            <ExploreCourses />
          </StudentDashboardLayout>
        } />
        <Route path="/student/library" element={
          <StudentDashboardLayout>
            <Library />
          </StudentDashboardLayout>
        } />
        <Route path="/student/assignments" element={
          <StudentDashboardLayout>
            <Assignments />
          </StudentDashboardLayout>
        } />
        <Route path="/student/profile" element={
          <StudentDashboardLayout>
            <StudentProfile />
          </StudentDashboardLayout>
        } />

        {/* Admin Dashboard Routes */}
        <Route path="/dashboard" element={
          <DashboardLayout>
            <Dashboard />
          </DashboardLayout>
        } />
        <Route path="/dashboard/courses" element={
          <DashboardLayout>
            <Courses />
          </DashboardLayout>
        } />
        <Route path="/dashboard/courses/add" element={
          <DashboardLayout>
            <AddCourse />
          </DashboardLayout>
        } />
        <Route path="/dashboard/students" element={
          <DashboardLayout>
            <Students />
          </DashboardLayout>
        } />
        <Route path="/dashboard/schedule" element={
          <DashboardLayout>
            <Schedule />
          </DashboardLayout>
        } />
        <Route path="/dashboard/paymentHistory" element={
          <DashboardLayout>
            <PaymentHistory />
          </DashboardLayout>    
        } />

        {/* Coming Soon Pages */}
        
        <Route path="/dashboard/courses/categories" element={
          <DashboardLayout>
            <ComingSoon title="Course Categories" description="Course category management" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/students/add" element={
          <DashboardLayout>
            <ComingSoon title="Add Student" description="Student enrollment feature" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/students/groups" element={
          <DashboardLayout>
            <ComingSoon title="Student Groups" description="Student group management" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/instruments" element={
          <DashboardLayout>
            <ComingSoon title="Instruments" description="Instrument management system" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/reports" element={
          <DashboardLayout>
            <ComingSoon title="Reports" description="Analytics and reporting dashboard" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/notifications" element={
          <DashboardLayout>
            <ComingSoon title="Notifications" description="Notification management system" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/documents" element={
          <DashboardLayout>
            <ComingSoon title="Documents" description="Document management system" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/profile" element={
          <DashboardLayout>
            <ComingSoon title="Profile" description="User profile management" />
          </DashboardLayout>
        } />
        <Route path="/dashboard/settings" element={
          <DashboardLayout>
            <ComingSoon title="Settings" description="System settings and configuration" />
          </DashboardLayout>
        } />

        {/* Redirect root to signin */}
        <Route path="/" element={<Navigate to="/signin" replace />} />

        {/* Catch all route - redirect to signin */}
        <Route path="*" element={<Navigate to="/signin" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
