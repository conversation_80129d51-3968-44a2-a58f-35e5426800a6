/* Tailwind Core */
@import 'tailwindcss';

/* <PERSON><PERSON> Configs */
@import './config.reui.css';

/* Custom Dashboard Styles */
.dashboard-card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 transition-all duration-200 hover:shadow-lg;
}

.dashboard-card-header {
  @apply p-6 border-b border-gray-200;
}

.dashboard-card-content {
  @apply p-6;
}

.sidebar-menu-item {
  @apply flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200;
}

.sidebar-menu-item-active {
  @apply bg-music-background-orange text-white shadow-sm;
}

.sidebar-menu-item-inactive {
  @apply text-music-light hover:bg-music-background-orange/20 hover:text-music-background-light;
}

.sidebar-submenu-item {
  @apply block px-3 py-2 rounded-lg text-sm transition-all duration-200;
}

.sidebar-submenu-item-active {
  @apply bg-music-background-orange text-white;
}

.sidebar-submenu-item-inactive {
  @apply text-music-light hover:bg-music-background-orange/20 hover:text-music-background-light;
}

/* Smooth animations */
.sidebar-transition {
  @apply transition-transform duration-300 ease-in-out;
}

/* Custom scrollbar for sidebar */
.sidebar-scroll::-webkit-scrollbar {
  width: 5px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(112, 61, 5, 0.856);
  border-radius: 10px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(183, 116, 42, 0.5);
}

/* Stats card animations */
.stats-card {
  @apply transform transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

/* Main content area */
.main-content {
  @apply bg-gradient-to-br from-gray-50 to-gray-100;
  min-height: calc(100vh - 80px); /* Account for header height */
}

/* Page header styling */
.page-header {
  @apply bg-gradient-to-r from-white to-gray-50 rounded-xl shadow-sm border border-gray-200 p-8 mb-8;
}

/* Improved spacing */
.dashboard-section {
  @apply space-y-8;
}

/* Ensure content is fully scrollable */
.content-wrapper {
  @apply min-h-full;
}

/* Custom scrollbar for main content */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Button styles */
.btn-primary {
  @apply bg-music-background-orange text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-music-background-orange/90 hover:shadow-md;
}

.btn-secondary {
  @apply bg-white text-gray-700 px-4 py-2 rounded-lg font-medium border border-gray-300 transition-all duration-200 hover:bg-gray-50 hover:shadow-sm;
}

/* Table styles */
.dashboard-table {
  @apply w-full;
}

.dashboard-table th {
  @apply text-left py-3 px-6 font-medium text-gray-900 bg-gray-50 border-b border-gray-200;
}

.dashboard-table td {
  @apply py-4 px-6 border-b border-gray-200;
}

.dashboard-table tr:hover {
  @apply bg-gray-50;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent transition-all duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* Badge styles */
.badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}