import { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  Plus, 
  Filter,
  ChevronLeft,
  ChevronRight,
  Music,
  Users,
  MapPin
} from 'lucide-react';

const Schedule = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('week'); // 'week' or 'day'

  const scheduleData = [
    {
      id: 1,
      title: 'Tabla Basics - Group A',
      instructor: '<PERSON>',
      time: '09:00 AM - 10:30 AM',
      duration: '1h 30m',
      students: 12,
      room: 'Room A1',
      type: 'Group Class',
      color: 'bg-blue-500'
    },
    {
      id: 2,
      title: 'Sitar Individual Session',
      instructor: '<PERSON>',
      time: '11:00 AM - 12:00 PM',
      duration: '1h',
      students: 1,
      room: 'Room B2',
      type: 'Individual',
      color: 'bg-green-500'
    },
    {
      id: 3,
      title: 'Vocal Training - Advanced',
      instructor: '<PERSON>',
      time: '02:00 PM - 03:30 PM',
      duration: '1h 30m',
      students: 8,
      room: 'Main Hall',
      type: 'Group Class',
      color: 'bg-purple-500'
    },
    {
      id: 4,
      title: 'Harmonium Workshop',
      instructor: '<PERSON>',
      time: '04:00 PM - 05:30 PM',
      duration: '1h 30m',
      students: 15,
      room: 'Room A1',
      type: 'Workshop',
      color: 'bg-orange-500'
    },
    {
      id: 5,
      title: 'Flute Fundamentals',
      instructor: 'Guru Maya Singh',
      time: '06:00 PM - 07:00 PM',
      duration: '1h',
      students: 10,
      room: 'Room C3',
      type: 'Group Class',
      color: 'bg-pink-500'
    }
  ];

  const timeSlots = [
    '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '01:00 PM', '02:00 PM', '03:00 PM', '04:00 PM',
    '05:00 PM', '06:00 PM', '07:00 PM', '08:00 PM'
  ];

  const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const navigateDate = (direction) => {
    const newDate = new Date(currentDate);
    if (viewMode === 'week') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 1 : -1));
    }
    setCurrentDate(newDate);
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Schedule</h1>
          <p className="text-gray-600 text-xl">Manage classes and appointments</p>
        </div>
        <button className="btn-primary flex items-center gap-2 px-6 py-3 text-lg">
          <Plus size={20} />
          Schedule Class
        </button>
      </div>

      {/* Schedule Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          {/* Date Navigation */}
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigateDate('prev')}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <ChevronLeft size={20} />
            </button>
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900">
                {formatDate(currentDate)}
              </h2>
            </div>
            <button
              onClick={() => navigateDate('next')}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <ChevronRight size={20} />
            </button>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('day')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'day' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Day
              </button>
              <button
                onClick={() => setViewMode('week')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'week' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Week
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Today's Schedule */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Today's Classes</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {scheduleData.map((item) => (
              <div key={item.id} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className={`w-1 h-16 ${item.color} rounded-full`}></div>
                
                <div className="flex-1">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">{item.title}</h3>
                      <p className="text-sm text-gray-600">{item.instructor}</p>
                    </div>
                    <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                      {item.type}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-6 mt-2 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Clock size={14} />
                      {item.time}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users size={14} />
                      {item.students} students
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin size={14} />
                      {item.room}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Edit
                  </button>
                  <button className="px-3 py-1 text-sm bg-music-background-orange text-white rounded-lg hover:bg-music-background-orange/90 transition-colors">
                    Start Class
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Today's Classes</p>
              <p className="text-2xl font-bold text-gray-900">8</p>
            </div>
            <Calendar className="text-blue-500" size={24} />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Students Today</p>
              <p className="text-2xl font-bold text-gray-900">64</p>
            </div>
            <Users className="text-green-500" size={24} />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Instructors</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
            <Music className="text-purple-500" size={24} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Schedule;
