import { useState } from 'react';
import { 
  FileText, 
  Clock, 
  Calendar, 
  CheckCircle,
  AlertTriangle,
  Upload,
  Download,
  Eye,
  Star,
  Filter,
  Search,
  ChevronDown
} from 'lucide-react';

const Assignments = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [selectedType, setSelectedType] = useState('all');

  const courses = ['All Courses', 'Tabla Fundamentals', 'Classical Vocal Training', 'Sitar Intermediate'];
  const assignmentTypes = ['All Types', 'Practice', 'Theory', 'Performance', 'Quiz'];

  const assignments = [
    {
      id: 1,
      title: 'Tabla Rhythm Pattern Practice',
      course: 'Tabla Fundamentals',
      instructor: '<PERSON>',
      type: 'Practice',
      description: 'Record yourself playing the basic rhythm patterns learned in Module 3',
      dueDate: '2024-12-20',
      assignedDate: '2024-12-15',
      maxScore: 100,
      status: 'pending',
      priority: 'high',
      requirements: ['Audio recording (min 3 minutes)', 'Practice at least 5 patterns', 'Clear audio quality'],
      submissionFormat: 'Audio file (MP3/WAV)',
      estimatedTime: '2-3 hours'
    },
    {
      id: 2,
      title: 'Raga Identification Quiz',
      course: 'Classical Vocal Training',
      instructor: 'Smt. Priya Sharma',
      type: 'Quiz',
      description: 'Online quiz to test your knowledge of major ragas and their characteristics',
      dueDate: '2024-12-18',
      assignedDate: '2024-12-12',
      maxScore: 50,
      status: 'pending',
      priority: 'medium',
      requirements: ['Complete within 30 minutes', 'Single attempt only', 'No external resources'],
      submissionFormat: 'Online quiz',
      estimatedTime: '30 minutes'
    },
    {
      id: 3,
      title: 'Sitar Tuning Demonstration',
      course: 'Sitar Intermediate',
      instructor: 'Pt. Ravi Shankar',
      type: 'Performance',
      description: 'Video demonstration of proper sitar tuning process',
      dueDate: '2024-12-25',
      assignedDate: '2024-12-10',
      maxScore: 75,
      status: 'pending',
      priority: 'low',
      requirements: ['Video recording (5-10 minutes)', 'Show complete tuning process', 'Explain each step'],
      submissionFormat: 'Video file (MP4/MOV)',
      estimatedTime: '1-2 hours'
    },
    {
      id: 4,
      title: 'Music Theory Assignment',
      course: 'Classical Vocal Training',
      instructor: 'Smt. Priya Sharma',
      type: 'Theory',
      description: 'Written assignment on the structure and characteristics of Raga Yaman',
      dueDate: '2024-12-15',
      assignedDate: '2024-12-08',
      maxScore: 100,
      score: 85,
      status: 'graded',
      priority: 'medium',
      feedback: 'Excellent understanding of the raga structure. Good analysis of the characteristic phrases.',
      submittedDate: '2024-12-14',
      submissionFormat: 'PDF document',
      estimatedTime: '3-4 hours'
    },
    {
      id: 5,
      title: 'Basic Hand Position Practice',
      course: 'Tabla Fundamentals',
      instructor: 'Guru Ramesh Kumar',
      type: 'Practice',
      description: 'Practice and record basic tabla hand positions',
      dueDate: '2024-12-10',
      assignedDate: '2024-12-05',
      maxScore: 80,
      score: 92,
      status: 'graded',
      priority: 'high',
      feedback: 'Outstanding technique! Your hand positions are very precise. Keep up the excellent work.',
      submittedDate: '2024-12-09',
      submissionFormat: 'Video file',
      estimatedTime: '2 hours'
    },
    {
      id: 6,
      title: 'Finger Exercise Recording',
      course: 'Sitar Intermediate',
      instructor: 'Pt. Ravi Shankar',
      type: 'Practice',
      description: 'Record finger exercises from Lesson 8',
      dueDate: '2024-12-08',
      assignedDate: '2024-12-03',
      maxScore: 60,
      score: 78,
      status: 'graded',
      priority: 'medium',
      feedback: 'Good progress on finger dexterity. Work on maintaining consistent tempo.',
      submittedDate: '2024-12-07',
      submissionFormat: 'Audio file',
      estimatedTime: '1.5 hours'
    }
  ];

  const filteredAssignments = assignments.filter(assignment => {
    const matchesTab = activeTab === 'all' || 
                      (activeTab === 'pending' && assignment.status === 'pending') ||
                      (activeTab === 'submitted' && assignment.status === 'submitted') ||
                      (activeTab === 'graded' && assignment.status === 'graded');
    
    const matchesSearch = assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCourse = selectedCourse === 'all' || assignment.course === selectedCourse;
    const matchesType = selectedType === 'all' || assignment.type === selectedType;
    
    return matchesTab && matchesSearch && matchesCourse && matchesType;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'submitted': return 'text-blue-600 bg-blue-100';
      case 'graded': return 'text-green-600 bg-green-100';
      case 'overdue': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'Practice': return Upload;
      case 'Theory': return FileText;
      case 'Performance': return Star;
      case 'Quiz': return CheckCircle;
      default: return FileText;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysUntilDue = (dueDate) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Assignments</h1>
        <p className="text-gray-600 text-xl">Track your assignments, submissions, and grades</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6 w-fit">
        {[
          { key: 'pending', label: 'Pending', count: assignments.filter(a => a.status === 'pending').length },
          { key: 'graded', label: 'Graded', count: assignments.filter(a => a.status === 'graded').length },
          { key: 'all', label: 'All', count: assignments.length }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2 ${
              activeTab === tab.key
                ? 'bg-white text-music-background-orange shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            {tab.label}
            <span className={`px-2 py-0.5 rounded-full text-xs ${
              activeTab === tab.key ? 'bg-music-background-orange text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              {tab.count}
            </span>
          </button>
        ))}
      </div>

      {/* Search and Filters */}
      <div className="dashboard-card mb-6">
        <div className="dashboard-card-content">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="md:col-span-2 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search assignments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
              />
            </div>

            {/* Course Filter */}
            <div className="relative">
              <select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                {courses.map(course => (
                  <option key={course} value={course === 'All Courses' ? 'all' : course}>{course}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>

            {/* Type Filter */}
            <div className="relative">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                {assignmentTypes.map(type => (
                  <option key={type} value={type === 'All Types' ? 'all' : type}>{type}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>
          </div>
        </div>
      </div>

      {/* Assignments List */}
      <div className="space-y-4">
        {filteredAssignments.map((assignment) => {
          const TypeIcon = getTypeIcon(assignment.type);
          const daysUntilDue = getDaysUntilDue(assignment.dueDate);
          const isOverdue = daysUntilDue < 0 && assignment.status === 'pending';
          
          return (
            <div key={assignment.id} className="dashboard-card hover:shadow-md transition-shadow">
              <div className="dashboard-card-content">
                <div className="flex items-start gap-4">
                  {/* Assignment Icon */}
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    assignment.type === 'Practice' ? 'text-blue-600 bg-blue-100' :
                    assignment.type === 'Theory' ? 'text-purple-600 bg-purple-100' :
                    assignment.type === 'Performance' ? 'text-green-600 bg-green-100' :
                    'text-orange-600 bg-orange-100'
                  }`}>
                    <TypeIcon size={24} />
                  </div>

                  {/* Assignment Info */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-gray-800 mb-1">{assignment.title}</h3>
                        <p className="text-sm text-gray-600">
                          {assignment.course} • by {assignment.instructor}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(isOverdue ? 'overdue' : assignment.status)}`}>
                          {isOverdue ? 'Overdue' : assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(assignment.priority)}`}>
                          {assignment.priority.charAt(0).toUpperCase() + assignment.priority.slice(1)}
                        </span>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 mb-3">{assignment.description}</p>

                    {/* Assignment Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500 mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar size={14} />
                        <span>Due: {formatDate(assignment.dueDate)}</span>
                        {assignment.status === 'pending' && (
                          <span className={`ml-2 ${isOverdue ? 'text-red-600' : daysUntilDue <= 2 ? 'text-orange-600' : 'text-gray-500'}`}>
                            ({isOverdue ? `${Math.abs(daysUntilDue)} days overdue` : `${daysUntilDue} days left`})
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock size={14} />
                        <span>Est. time: {assignment.estimatedTime}</span>
                      </div>
                      <div>
                        <span>Max score: {assignment.maxScore} points</span>
                      </div>
                    </div>

                    {/* Score and Feedback for graded assignments */}
                    {assignment.status === 'graded' && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-green-800">Score: {assignment.score}/{assignment.maxScore}</span>
                          <span className="text-sm text-green-600">
                            {Math.round((assignment.score / assignment.maxScore) * 100)}%
                          </span>
                        </div>
                        {assignment.feedback && (
                          <p className="text-sm text-green-700">{assignment.feedback}</p>
                        )}
                      </div>
                    )}

                    {/* Requirements for pending assignments */}
                    {assignment.status === 'pending' && assignment.requirements && (
                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-1">Requirements:</p>
                        <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                          {assignment.requirements.map((req, index) => (
                            <li key={index}>{req}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2">
                    {assignment.status === 'pending' && (
                      <button className="bg-music-background-orange hover:bg-music-background text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                        <Upload size={16} />
                        Submit
                      </button>
                    )}
                    {assignment.status === 'graded' && (
                      <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                        <Eye size={16} />
                        View Details
                      </button>
                    )}
                    <button className="px-3 py-2 border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors flex items-center gap-2 text-sm">
                      <Download size={14} />
                      Instructions
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredAssignments.length === 0 && (
        <div className="text-center py-12">
          <FileText size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No assignments found</h3>
          <p className="text-gray-500">
            {searchTerm || selectedCourse !== 'all' || selectedType !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : `No ${activeTab} assignments at the moment`
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default Assignments;
