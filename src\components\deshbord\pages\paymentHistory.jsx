import { useState } from 'react';
import {
  Search,
  Filter,
  Eye,
  Download,
  CreditCard,
  Calendar,
  User,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw
} from 'lucide-react';

const PaymentHistory = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterMethod, setFilterMethod] = useState('all');

  const payments = [
    {
      id: 'PAY001',
      studentName: '<PERSON><PERSON><PERSON>',
      studentEmail: '<EMAIL>',
      course: 'Tabla Basics',
      amount: 950,
      currency: 'AED',
      paymentMethod: 'Credit Card',
      transactionId: 'TXN123456789',
      status: 'Completed',
      paymentDate: '2024-03-15',
      dueDate: '2024-03-10',
      description: 'Course Fee - Tabla Basics (3 months)'
    },
    {
      id: 'PAY002',
      studentName: '<PERSON><PERSON>',
      studentEmail: '<EMAIL>',
      course: 'Sitar Intermediate',
      amount: 1140,
      currency: 'AED',
      paymentMethod: 'Credit Card',
      transactionId: 'TXN987654321',
      status: 'Completed',
      paymentDate: '2024-03-14',
      dueDate: '2024-03-12',
      description: 'Course Fee - Sitar Intermediate (6 months)'
    },
    {
      id: 'PAY003',
      studentName: 'Rahul Kumar',
      studentEmail: '<EMAIL>',
      course: 'Vocal Training',
      amount: 760,
      currency: 'AED',
      paymentMethod: 'Debit Card',
      transactionId: 'TXN456789123',
      status: 'Pending',
      paymentDate: null,
      dueDate: '2024-03-20',
      description: 'Course Fee - Vocal Training (2 months)'
    },
    {
      id: 'PAY004',
      studentName: 'Anita Singh',
      studentEmail: '<EMAIL>',
      course: 'Harmonium Advanced',
      amount: 1490,
      currency: 'AED',
      paymentMethod: 'Credit Card',
      transactionId: 'TXN789123456',
      status: 'Failed',
      paymentDate: '2024-03-13',
      dueDate: '2024-03-08',
      description: 'Course Fee - Harmonium Advanced (12 months)'
    },
    {
      id: 'PAY005',
      studentName: 'Vikram Joshi',
      studentEmail: '<EMAIL>',
      course: 'Flute Fundamentals',
      amount: 870,
      currency: 'AED',
      paymentMethod: 'Debit Card',
      transactionId: 'TXN321654987',
      status: 'Completed',
      paymentDate: '2024-03-12',
      dueDate: '2024-03-05',
      description: 'Course Fee - Flute Fundamentals (4 months)'
    },
    {
      id: 'PAY006',
      studentName: 'Meera Gupta',
      studentEmail: '<EMAIL>',
      course: 'Mridangam Mastery',
      amount: 1630,
      currency: 'AED',
      paymentMethod: 'Credit Card',
      transactionId: 'TXN654987321',
      status: 'Refunded',
      paymentDate: '2024-03-10',
      dueDate: '2024-03-01',
      description: 'Course Fee - Mridangam Mastery (8 months)'
    }
  ];

  const statuses = ['all', 'Completed', 'Pending', 'Failed', 'Refunded'];
  const paymentMethods = ['all', 'Credit Card', 'Debit Card', 'Bank Transfer', 'Digital Wallet'];

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || payment.status === filterStatus;
    const matchesMethod = filterMethod === 'all' || payment.paymentMethod === filterMethod;
    return matchesSearch && matchesStatus && matchesMethod;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      case 'Refunded': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed': return <CheckCircle size={14} className="text-green-600" />;
      case 'Pending': return <Clock size={14} className="text-yellow-600" />;
      case 'Failed': return <XCircle size={14} className="text-red-600" />;
      case 'Refunded': return <RefreshCw size={14} className="text-blue-600" />;
      default: return <Clock size={14} className="text-gray-600" />;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount, currency) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Calculate stats
  const totalPayments = payments.length;
  const completedPayments = payments.filter(p => p.status === 'Completed').length;
  const pendingPayments = payments.filter(p => p.status === 'Pending').length;
  const totalRevenue = payments.filter(p => p.status === 'Completed').reduce((sum, p) => sum + p.amount, 0);

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Payment History</h1>
          <p className="text-gray-600 text-xl">Track and manage all student payments</p>
        </div>
        <div className="flex gap-3">
          <button className="btn-secondary flex items-center gap-2 px-4 py-2">
            <Download size={18} />
            Export
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Payments</p>
              <p className="text-2xl font-bold text-gray-900">{totalPayments}</p>
            </div>
            <CreditCard className="text-blue-500" size={24} />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{completedPayments}</p>
            </div>
            <CheckCircle className="text-green-500" size={24} />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{pendingPayments}</p>
            </div>
            <Clock className="text-yellow-500" size={24} />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-music-background">{formatCurrency(totalRevenue, 'AED')}</p>
            </div>
            <DollarSign className="text-music-background" size={24} />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search by student name, email, course, or payment ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div className="flex items-center gap-2">
            <Filter size={18} className="text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
            >
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status === 'all' ? 'All Status' : status}
                </option>
              ))}
            </select>
          </div>

          {/* Payment Method Filter */}
          <div className="flex items-center gap-2">
            <CreditCard size={18} className="text-gray-400" />
            <select
              value={filterMethod}
              onChange={(e) => setFilterMethod(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
            >
              {paymentMethods.map(method => (
                <option key={method} value={method}>
                  {method === 'all' ? 'All Methods' : method}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Payment History Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Payment ID</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Student</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Course</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Amount</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Method</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Date</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-6 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="py-4 px-6">
                    <div className="font-medium text-gray-900">{payment.id}</div>
                    <div className="text-sm text-gray-500">{payment.transactionId}</div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-music-background-orange rounded-full flex items-center justify-center">
                        <User size={16} className="text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{payment.studentName}</p>
                        <p className="text-sm text-gray-500">{payment.studentEmail}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-gray-900">{payment.course}</div>
                    <div className="text-sm text-gray-500">{payment.description}</div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="font-semibold text-gray-900">
                      {formatCurrency(payment.amount, payment.currency)}
                    </div>
                    <div className="text-sm text-gray-500">
                      Due: {formatDate(payment.dueDate)}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      <CreditCard size={16} className="text-gray-400" />
                      <span className="text-gray-900">{payment.paymentMethod}</span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-1 text-gray-600">
                      <Calendar size={14} />
                      <span className="text-sm">{formatDate(payment.paymentDate)}</span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(payment.status)}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {payment.status}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors" title="View Details">
                        <Eye size={16} />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-green-600 transition-colors" title="Download Receipt">
                        <Download size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredPayments.length === 0 && (
        <div className="text-center py-12">
          <CreditCard size={48} className="text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No payments found</h3>
          <p className="text-gray-600 mb-4">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default PaymentHistory;