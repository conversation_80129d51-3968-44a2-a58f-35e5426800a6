import { 
  BookOpen, 
  Calendar, 
  Clock,
  Award,
  TrendingUp,
  Play,
  FileText,
  Users,
  ChevronRight,
  Target,
  CheckCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';

const StudentDashboard = () => {
  const stats = [
    {
      title: 'Enrolled Courses',
      value: '4',
      change: '+1 this month',
      changeType: 'positive',
      icon: BookOpen,
      color: 'bg-blue-500'
    },
    {
      title: 'Completed Lessons',
      value: '28',
      change: '+5 this week',
      changeType: 'positive',
      icon: CheckCircle,
      color: 'bg-green-500'
    },
    {
      title: 'Practice Hours',
      value: '42h',
      change: '+8h this week',
      changeType: 'positive',
      icon: Clock,
      color: 'bg-music-background-orange'
    },
    {
      title: 'Assignments Due',
      value: '3',
      change: '2 due today',
      changeType: 'warning',
      icon: FileText,
      color: 'bg-red-500'
    }
  ];

  const activeCourses = [
    {
      id: 1,
      title: 'Tabla Fundamentals',
      instructor: '<PERSON>',
      progress: 75,
      nextClass: '2024-12-18 14:00',
      totalLessons: 20,
      completedLessons: 15,
      image: '/api/placeholder/300/200'
    },
    {
      id: 2,
      title: 'Classical Vocal Training',
      instructor: 'Smt. Priya <PERSON>',
      progress: 60,
      nextClass: '2024-12-19 16:30',
      totalLessons: 24,
      completedLessons: 14,
      image: '/api/placeholder/300/200'
    },
    {
      id: 3,
      title: 'Sitar Intermediate',
      instructor: 'Pt. Ravi <PERSON>',
      progress: 40,
      nextClass: '2024-12-20 15:00',
      totalLessons: 18,
      completedLessons: 7,
      image: '/api/placeholder/300/200'
    }
  ];

  const upcomingClasses = [
    {
      id: 1,
      title: 'Tabla Practice Session',
      instructor: 'Guru Ramesh Kumar',
      time: '2:00 PM - 3:00 PM',
      date: 'Today',
      type: 'Live Class',
      canJoin: true
    },
    {
      id: 2,
      title: 'Vocal Technique Workshop',
      instructor: 'Smt. Priya Sharma',
      time: '4:30 PM - 5:30 PM',
      date: 'Today',
      type: 'Workshop',
      canJoin: false
    },
    {
      id: 3,
      title: 'Sitar Theory Class',
      instructor: 'Pt. Ravi Shankar',
      time: '3:00 PM - 4:00 PM',
      date: 'Tomorrow',
      type: 'Theory',
      canJoin: false
    }
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'assignment',
      title: 'Submitted Tabla Rhythm Assignment',
      time: '2 hours ago',
      icon: FileText,
      color: 'text-green-600'
    },
    {
      id: 2,
      type: 'class',
      title: 'Attended Vocal Training Session',
      time: '1 day ago',
      icon: Play,
      color: 'text-blue-600'
    },
    {
      id: 3,
      type: 'achievement',
      title: 'Completed Module 3 - Tabla Basics',
      time: '2 days ago',
      icon: Award,
      color: 'text-music-background-orange'
    },
    {
      id: 4,
      type: 'material',
      title: 'Downloaded Sitar Practice Guide',
      time: '3 days ago',
      icon: BookOpen,
      color: 'text-purple-600'
    }
  ];

  return (
    <div className="dashboard-section">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Welcome back, Student!</h1>
        <p className="text-gray-600 text-xl">Continue your musical journey and track your progress</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="dashboard-card">
            <div className="dashboard-card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className={`text-sm mt-1 ${
                    stat.changeType === 'positive' ? 'text-green-600' : 
                    stat.changeType === 'warning' ? 'text-orange-600' : 'text-gray-500'
                  }`}>
                    {stat.change}
                  </p>
                </div>
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                  <stat.icon className="text-white" size={24} />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Active Courses */}
        <div className="lg:col-span-2">
          <div className="dashboard-card">
            <div className="dashboard-card-header">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-800">Active Courses</h2>
                <Link 
                  to="/student/courses/active"
                  className="text-music-background-orange hover:text-music-background font-medium text-sm flex items-center gap-1"
                >
                  View All <ChevronRight size={16} />
                </Link>
              </div>
            </div>
            <div className="dashboard-card-content">
              <div className="space-y-4">
                {activeCourses.map((course) => (
                  <div key={course.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <BookOpen size={24} className="text-gray-500" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800 mb-1">{course.title}</h3>
                        <p className="text-sm text-gray-600 mb-2">by {course.instructor}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                          <span>{course.completedLessons}/{course.totalLessons} lessons</span>
                          <span>Next: {new Date(course.nextClass).toLocaleDateString()}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-music-background-orange h-2 rounded-full transition-all duration-300"
                            style={{ width: `${course.progress}%` }}
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{course.progress}% complete</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Upcoming Classes */}
          <div className="dashboard-card">
            <div className="dashboard-card-header">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800">Upcoming Classes</h2>
                <Link 
                  to="/student/classes"
                  className="text-music-background-orange hover:text-music-background font-medium text-sm"
                >
                  View All
                </Link>
              </div>
            </div>
            <div className="dashboard-card-content">
              <div className="space-y-3">
                {upcomingClasses.map((classItem) => (
                  <div key={classItem.id} className="border-l-4 border-music-background-orange pl-4 py-2">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-800 text-sm">{classItem.title}</h4>
                        <p className="text-xs text-gray-600">{classItem.instructor}</p>
                        <p className="text-xs text-gray-500 mt-1">{classItem.date} • {classItem.time}</p>
                      </div>
                      {classItem.canJoin && (
                        <button className="bg-green-500 text-white text-xs px-3 py-1 rounded-full hover:bg-green-600 transition-colors">
                          Join
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="dashboard-card">
            <div className="dashboard-card-header">
              <h2 className="text-lg font-semibold text-gray-800">Recent Activity</h2>
            </div>
            <div className="dashboard-card-content">
              <div className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className={`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center ${activity.color}`}>
                      <activity.icon size={14} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-800">{activity.title}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
