import Layout from './layout/Layout';



const Forget = () => {
  return (
    <Layout>
        <div className="w-full  text-white p-8 ">
        <h2 className="text-3xl font-semibold text-center">Forgot Password</h2>
        <p className="text-center text-md mt-2 mb-4">Enter your email / phone number to reset your password.</p>
        <hr className="border-t border-white mb-6" />

        <form onSubmit={(e) => e.preventDefault()} className="space-y-4">

          {/* Email */}
          <div>
            <label className="block text-sm mb-1">Email Id / Phone Number</label>
            <input
              type="email"
              name="email"
              required
              className="w-full px-3 py-2 rounded-md text-black border bg-white border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-400"
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-[#3E1900] hover:bg-[#2e1200] text-white font-semibold py-2 rounded-md text-sm transition-colors"
          >
            Submit
          </button>

          {/* Sign Up */}
          <div className='text-center text-sm'>
          <a className="cursor-pointer inline-flex items-center gap-2 text-sm font-semibold text-accent-foreground hover:underline hover:underline-offset-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-left size-3.5 opacity-70" aria-hidden="true"><path d="M6 8L2 12L6 16"></path><path d="M2 12H22"></path></svg>
           Back to Sign In

          </a>
            </div>
        </form>
      </div>
    </Layout>
  );
};

export default Forget;