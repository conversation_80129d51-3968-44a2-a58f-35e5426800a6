import Layout from './layout/Layout';
import { Eye } from 'lucide-react';
import { useState } from 'react';

const Signin = () => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <Layout>
        <div className="w-full  text-white p-8 ">
        <h2 className="text-3xl font-semibold text-center">Sign In</h2>
        <p className="text-center text-md mt-2 mb-4">Welcome back! Log in with your credentials.</p>
        <hr className="border-t border-white mb-6" />

        <form onSubmit={(e) => e.preventDefault()} className="space-y-4">

          {/* Email */}
          <div>
            <label className="block text-sm mb-1">Email Id / Phone Number</label>
            <input
              type="email"
              name="email"
              required
              className="w-full px-3 py-2 rounded-md text-black border bg-white border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-400"
            />
          </div>

          {/* Password */}
          <div>
            <label className="block text-sm mb-1">Password</label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                required
                className="w-full px-3 py-2 rounded-md bg-white text-black border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-400 pr-10"
              />
              <Eye
                size={18}
                className="absolute right-3 top-2.5 text-gray-500 cursor-pointer"
                onClick={() => setShowPassword((prev) => !prev)}
              />
            </div>
          </div>

          {/* Remember Me + Forgot */}
          <div className="flex justify-between items-center text-sm">
            <label className="flex items-center gap-2">
              <input type="checkbox"  name="remember" className=" h-4 w-4 accent-music-background hover:accent-music-background" />
              Remember me
            </label>
            <a href="#" className="text-white font-medium hover:text-music-background transition-colors">
              Forgot Password?
            </a>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-[#3E1900] hover:bg-[#2e1200] text-white font-semibold py-2 rounded-md text-sm transition-colors"
          >
            Sign In
          </button>

          {/* Sign Up */}
          <p className="text-center text-sm mt-2">
            Don’t have an account?{" "}
            <a href="#" className="text-white hover:text-music-background font-semibold hover:underline">
              Sign Up
            </a>
          </p>
        </form>
      </div>
    </Layout>
  );
};

export default Signin;
    