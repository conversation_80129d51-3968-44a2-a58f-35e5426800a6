import Layout from './layout/Layout';
import { Eye } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Signin = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [userRole, setUserRole] = useState('student');
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();

    // Simple demo authentication - in real app, this would be API call
    if (formData.email && formData.password) {
      // Redirect based on user role
      if (userRole === 'admin') {
        navigate('/dashboard');
      } else {
        navigate('/student/dashboard');
      }
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <Layout>
        <div className="w-full  text-white p-8 ">
        <h2 className="text-3xl font-semibold text-center">Sign In</h2>
        <p className="text-center text-md mt-2 mb-4">Welcome back! Log in with your credentials.</p>
        <hr className="border-t border-white mb-6" />

        {/* Role Selection */}
        <div className="mb-6">
          <label className="block text-sm mb-2">Login as:</label>
          <div className="flex gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="role"
                value="student"
                checked={userRole === 'student'}
                onChange={(e) => setUserRole(e.target.value)}
                className="h-4 w-4 accent-music-background-orange"
              />
              <span>Student</span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="role"
                value="admin"
                checked={userRole === 'admin'}
                onChange={(e) => setUserRole(e.target.value)}
                className="h-4 w-4 accent-music-background-orange"
              />
              <span>Admin</span>
            </label>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">

          {/* Email */}
          <div>
            <label className="block text-sm mb-1">Email Id / Phone Number</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 rounded-md text-black border bg-white border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-400"
            />
          </div>

          {/* Password */}
          <div>
            <label className="block text-sm mb-1">Password</label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 rounded-md bg-white text-black border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-400 pr-10"
              />
              <Eye
                size={18}
                className="absolute right-3 top-2.5 text-gray-500 cursor-pointer"
                onClick={() => setShowPassword((prev) => !prev)}
              />
            </div>
          </div>

          {/* Remember Me + Forgot */}
          <div className="flex justify-between items-center text-sm">
            <label className="flex items-center gap-2">
              <input type="checkbox"  name="remember" className=" h-4 w-4 accent-music-background hover:accent-music-background" />
              Remember me
            </label>
            <a href="#" className="text-white font-medium hover:text-music-background transition-colors">
              Forgot Password?
            </a>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-[#3E1900] hover:bg-[#2e1200] text-white font-semibold py-2 rounded-md text-sm transition-colors"
          >
            Sign In
          </button>

          {/* Sign Up */}
          <p className="text-center text-sm mt-2">
            Don’t have an account?{" "}
            <a href="#" className="text-white hover:text-music-background font-semibold hover:underline">
              Sign Up
            </a>
          </p>
        </form>
      </div>
    </Layout>
  );
};

export default Signin;
    