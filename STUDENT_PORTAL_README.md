# SwaRas Academy - Student Portal

## Overview
The Student Portal is a comprehensive learning management system designed specifically for music students at SwaRas Academy. It provides a dedicated interface for students to manage their learning journey, access course materials, submit assignments, and track their progress.

## Features

### 🎯 Dashboard
- **Progress Overview**: Track your learning progress across all enrolled courses
- **Current Courses**: Quick access to active courses with progress indicators
- **Recent Activity**: View your latest activities and achievements
- **Upcoming Classes**: See scheduled classes with join links

### 📚 Classes
- **Upcoming Classes**: View scheduled classes with join functionality
- **Class History**: Access past classes with attendance tracking
- **Class Recordings**: Watch recordings of previous sessions
- **Search & Filter**: Find specific classes by instructor, course, or type

### 🎵 Courses
- **Active Courses**: Continue with enrolled courses and track module progress
- **Explore Courses**: Discover and enroll in new courses
- **Course Details**: View detailed course information, modules, and achievements
- **Progress Tracking**: Monitor completion status and scores

### 📖 Library
- **Learning Materials**: Access PDFs, videos, audio files, and images
- **Resource Categories**: Organized by course and material type
- **Search Functionality**: Find specific resources quickly
- **Download/View**: Access materials online or download for offline use

### 📝 Assignments
- **Pending Assignments**: View and submit upcoming assignments
- **Graded Work**: Check scores and feedback on completed assignments
- **Assignment Details**: View requirements, due dates, and submission formats
- **Progress Tracking**: Monitor assignment completion status

### 👤 Profile
- **Personal Information**: Update contact details and emergency contacts
- **Payment History**: View payment records and download invoices
- **Achievements**: Track earned badges and milestones
- **Learning Statistics**: View detailed learning analytics

## Navigation Structure

```
Student Portal
├── Dashboard - Overview and quick access
├── Classes - Upcoming classes and join links
├── Class Recordings - Archive of previous classes
├── Courses
│   ├── Active Courses - Enrolled courses with progress
│   └── New Courses - Explore and enroll
├── Library - Learning materials and resources
├── Assignments - Tests, homework, and submissions
├── Profile - Personal info and payment details
└── Logout
```

## Getting Started

### Login
1. Navigate to the login page
2. Select "Student" as your role
3. Use demo credentials:
   - Email: `<EMAIL>`
   - Password: `student123`

### First Steps
1. **Explore Dashboard**: Get familiar with your learning overview
2. **Check Classes**: See if you have any upcoming classes to join
3. **Review Courses**: Check your active courses and progress
4. **Complete Profile**: Update your personal information
5. **Browse Library**: Explore available learning materials

## Key Features Explained

### Class Management
- **Join Live Classes**: Click "Join Now" for scheduled classes
- **View Recordings**: Access past class recordings with search functionality
- **Track Attendance**: Monitor your class participation

### Course Progress
- **Module System**: Courses are organized into modules with individual lessons
- **Progress Tracking**: Visual progress bars show completion status
- **Achievements**: Earn badges for completing milestones

### Assignment System
- **Multiple Types**: Practice recordings, theory assignments, quizzes, and performances
- **Submission Tracking**: Monitor due dates and submission status
- **Feedback System**: Receive detailed feedback and scores from instructors

### Resource Library
- **Organized Content**: Materials categorized by course and type
- **Multiple Formats**: PDFs, videos, audio files, and images
- **Search & Filter**: Find specific resources quickly

## Technical Features

### Responsive Design
- **Mobile Friendly**: Optimized for tablets and smartphones
- **Desktop Experience**: Full-featured desktop interface
- **Touch Navigation**: Intuitive touch controls for mobile devices

### User Experience
- **Consistent Styling**: Matches the academy's brand colors and design
- **Intuitive Navigation**: Easy-to-use sidebar and header navigation
- **Quick Actions**: Fast access to frequently used features

### Performance
- **Fast Loading**: Optimized for quick page loads
- **Smooth Transitions**: Animated transitions between sections
- **Efficient Search**: Quick search across all content types

## Demo Data
The portal includes comprehensive demo data showcasing:
- 3 active courses with realistic progress
- Multiple upcoming and past classes
- Various assignment types and statuses
- Rich library of learning materials
- Complete student profile with payment history

## Support
For technical support or questions about using the Student Portal, please contact the SwaRas Academy support team.

---

**Note**: This is a demonstration version of the Student Portal. In a production environment, all data would be connected to a backend API with proper authentication and data persistence.
