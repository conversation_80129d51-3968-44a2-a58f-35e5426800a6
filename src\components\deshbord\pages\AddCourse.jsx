const AddCourse = () => {
  const Formstyle = "mt-2 flex w-full bg-background border border-input shadow-xs shadow-black/5 transition-[color,box-shadow] text-foreground placeholder:text-muted-foreground/80 placeholder:text-sm focus-visible:ring-ring/30 focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 [&[readonly]]:opacity-70 file:h-full [&[type=file]]:py-0 file:border-solid file:border-input file:bg-transparent file:font-medium file:not-italic file:text-foreground file:p-0 file:border-0 file:border-e aria-invalid:border-destructive/60 aria-invalid:ring-destructive/10 dark:aria-invalid:border-destructive dark:aria-invalid:ring-destructive/20 h-12 px-3 text-[0.8125rem] leading-(--text-sm--line-height) rounded-md file:pe-3 file:me-3"
  const labelstyle = "text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 font-medium text-music-light"

  return (
    <div className="min-h-screen mx-auto py-12 px-4">
      <div className="max-w-full mx-auto">
        <div className="bg-music-background-orange rounded-xl shadow-lg border border-music-light overflow-hidden">
          <div className="px-8 py-6 border-b border-music-light">
            <h2 className="text-4xl font-semibold text-music-light text-center">
              Create a Course
            </h2>
            <p className="mt-1 text-lg text-music-light text-center">
              Fill in the details below to create your course
            </p>
          </div>

          <form className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column */}
              <div className="space-y-4">
                {/* Package Name */}
                <div className="space-y-2">
                  <label htmlFor="packageName" className={labelstyle}>
                    Package Name
                  </label>
                  <input
                    type="text"
                    required
                    id="packageName"
                    className={Formstyle}
                    placeholder="Enter package name"
                  />
                </div>

                {/* Slug URL */}
                <div className="space-y-2">
                  <label htmlFor="slugUrl" className={labelstyle}>
                    Slug URL
                  </label>
                  <input
                    type="text"
                    id="slugUrl"
                    className={Formstyle}
                    placeholder="e.g. course-title-details"
                  />
                </div>

                {/* Payment URL */}
                <div className="space-y-2">
                  <label htmlFor="paymentUrl" className={labelstyle}>
                    Payment URL
                  </label>
                  <input
                    type="url"
                    id="paymentUrl"
                    className={Formstyle}
                    placeholder="e.g. https://payment.example.com"
                  />
                </div>

                {/* Description with Tabs */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <label className={labelstyle}>
                      Description (HTML Allowed)
                    </label>
                    <div className="flex space-x-1 bg-music-background-light p-1 rounded-lg">
                      <button
                        type="button"
                        className="px-3 py-1.5 text-sm font-medium rounded-md text-music-bg-background hover:text-bg-music-light"
                      >
                        Preview
                      </button>
                      <button
                        type="button"
                        className="px-3 py-1.5 text-sm font-medium rounded-md bg-music-background text-white"
                      >
                        Code
                      </button>
                    </div>
                  </div>

                  <textarea
                    id="description"
                    required
                    className={`${Formstyle} min-h-[12rem] pt-2`}
                    placeholder="Enter course description..."
                  />
                </div>

                {/* Duration */}
                <div className="space-y-2">
                  <label htmlFor="duration" className={labelstyle}>
                    Duration
                  </label>
                  <input
                    type="text"
                    id="duration"
                    required
                    className={Formstyle}
                    placeholder="e.g. 8 weeks"
                  />
                </div>

                {/* Start Date */}
                <div className="space-y-2">
                  <label htmlFor="startDate" className={labelstyle}>
                    Start Date
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    className={Formstyle}
                  />
                </div>

                {/* End Date */}
                <div className="space-y-2">
                  <label htmlFor="endDate" className={labelstyle}>
                    End Date
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    className={Formstyle}
                  />
                </div>

             
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                {/* Short Description */}
                <div className="space-y-2">
                  <label htmlFor="shortDescription" className={labelstyle}>
                    Short Description
                  </label>
                  <input
                    type="text"
                    id="shortDescription"
                    className={Formstyle}
                    placeholder="e.g. Learn Python in 8 weeks"
                  />
                </div>

                {/* Images Section */}
                <div className="space-y-6 border border-music-light p-6 rounded-xl">
                  <h3 className="text-lg font-medium text-music-light">Course Images</h3>
                  <div className="flex justify-between gap-4">
                  {/* Banner Image */}
                  <div className="space-y-2 w-1/2">
                    <label htmlFor="bannerImage" className={labelstyle}>
                      Banner Image
                    </label>
                    <div className="mt-1 flex justify-center py-2  border-2 border-input border-dashed rounded-lg">
                      <div className="space-y-1 text-center">
                        <div className="flex text-sm text-music-light">
                          <label
                            htmlFor="bannerImage"
                            className="relative cursor-pointer rounded-md font-medium text-music-background hover:text-music-background/90 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-music-background focus-within:ring-offset-music-background-orange"
                          >
                            <span>Upload a file</span>
                            <input
                              type="file"
                              id="bannerImage"
                              accept="image/*"
                              className="sr-only"
                            />
                          </label>

                        </div>

                      </div>
                    </div>
                  </div>

                  {/* Feature Image */}
                  <div className="space-y-2 w-1/2">
                    <label htmlFor="featureImage" className={labelstyle}>
                      Feature Image
                    </label>
                    <div className="mt-1 flex justify-center py-2  border-2 border-input border-dashed rounded-lg">
                      <div className="space-y-1 text-center">
                        <div className="flex text-sm text-music-light">
                          <label
                            htmlFor="featureImage"
                            className="relative cursor-pointer rounded-md font-medium text-music-background hover:text-music-background/90 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-music-background focus-within:ring-offset-music-background-orange"
                          >
                            <span>Upload a file</span>
                            <input
                              type="file"
                              id="featureImage"
                              accept="image/*"
                              required
                              className="sr-only"
                            />
                          </label>

                        </div>

                      </div>
                    </div>
                  </div>
                  </div>
                </div>

                {/* Pricing Section */}
                <div className="space-y-2 border border-music-light p-6 rounded-xl">
                  <h3 className="text-lg font-medium text-music-light">Course Pricing</h3>

                  {/* Default Price */}
                  <div className="space-y-2">
                    <label className={labelstyle}>
                      Default Price (USD)
                    </label>
                    <input
                      type="number"
                      required
                      className={Formstyle}
                      placeholder="Enter default price"
                    />
                  </div>

                  {/* Country Selector */}
                  <div className="space-y-2">
                    <label className={labelstyle}>
                      Add Country-Specific Price
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <select className={Formstyle}>
                        <option value="">Select country</option>
                        <option value="australia">Australia</option>
                        <option value="germany">Germany</option>
                        <option value="france">France</option>
                        <option value="japan">Japan</option>
                      </select>

                      <div className="flex gap-2 items-baseline">
                        <input
                          type="number"
                          placeholder="Price in USD"
                          className={Formstyle}
                        />
                        <button
                          type="button"
                          className="inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-music-background hover:bg-music-background/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-music-background focus:ring-offset-music-background-orange"
                        >
                          Add
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Discount price */}
                <div className="space-y-2 border border-music-light p-6 rounded-xl">
                  <h3 className="text-lg font-medium text-music-light">Course Pricing</h3>

                  

                  {/* Country Selector */}
                  <div className="space-y-2">
                    <label className={labelstyle}>
                      Payment Terms-wise Discount
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <input
                        type="number"
                        placeholder="Month"
                        className={Formstyle}
                      />

                      <div className="flex gap-2 items-baseline">
                        <input
                          type="number"
                          placeholder="Discount %"
                          className={Formstyle}
                        />
                        <button
                          type="button"
                          className="inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-music-background hover:bg-music-background/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-music-background focus:ring-offset-music-background-orange"
                        >
                          Add
                        </button>
                      </div>
                    </div>
                  </div>
                </div>



              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-8 pt-4 border-t border-music-light">
              <button
                type="submit"
                className="w-full py-4 px-6 bg-music-background hover:bg-music-background/90 text-white font-semibold rounded-lg transition-colors duration-200 text-lg"
              >
                Create Course
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddCourse;
