import { useState } from 'react';
import { 
  BookO<PERSON>, 
  Clock, 
  Users, 
  Star,
  Search,
  Filter,
  ChevronDown,
  Play,
  Award,
  Calendar,
  Heart,
  Share2
} from 'lucide-react';

const ExploreCourses = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [sortBy, setSortBy] = useState('popular');
  const [favorites, setFavorites] = useState([]);

  const categories = [
    'All Categories',
    'Percussion',
    'String Instruments',
    'Vocal Training',
    'Wind Instruments',
    'Music Theory'
  ];

  const levels = ['All Levels', 'Beginner', 'Intermediate', 'Advanced'];

  const availableCourses = [
    {
      id: 1,
      title: 'Harmonium Fundamentals',
      instructor: 'Pt. <PERSON><PERSON>adkar',
      description: 'Learn the basics of harmonium playing with traditional techniques and modern approaches',
      category: 'Wind Instruments',
      level: 'Beginner',
      duration: '2 months',
      totalLessons: 16,
      rating: 4.8,
      reviews: 124,
      students: 456,
      price: 2999,
      originalPrice: 3999,
      image: '/api/placeholder/400/250',
      features: ['Live Classes', 'Recorded Sessions', 'Practice Materials', 'Certificate'],
      preview: true,
      popular: true,
      new: false
    },
    {
      id: 2,
      title: 'Advanced Flute Techniques',
      instructor: 'Pt. Hariprasad Chaurasia',
      description: 'Master advanced flute techniques including complex ragas and improvisation',
      category: 'Wind Instruments',
      level: 'Advanced',
      duration: '4 months',
      totalLessons: 32,
      rating: 4.9,
      reviews: 89,
      students: 234,
      price: 5999,
      originalPrice: 7999,
      image: '/api/placeholder/400/250',
      features: ['Live Classes', 'One-on-One Sessions', 'Performance Opportunities', 'Certificate'],
      preview: true,
      popular: false,
      new: true
    },
    {
      id: 3,
      title: 'Violin for Beginners',
      instructor: 'Smt. L. Subramaniam',
      description: 'Start your violin journey with proper posture, bowing techniques, and basic melodies',
      category: 'String Instruments',
      level: 'Beginner',
      duration: '3 months',
      totalLessons: 24,
      rating: 4.7,
      reviews: 156,
      students: 678,
      price: 3499,
      originalPrice: 4499,
      image: '/api/placeholder/400/250',
      features: ['Live Classes', 'Recorded Sessions', 'Practice Tracks', 'Certificate'],
      preview: true,
      popular: true,
      new: false
    },
    {
      id: 4,
      title: 'Carnatic Vocal Intermediate',
      instructor: 'Smt. M.S. Subbulakshmi',
      description: 'Advance your Carnatic vocal skills with complex compositions and improvisation',
      category: 'Vocal Training',
      level: 'Intermediate',
      duration: '5 months',
      totalLessons: 40,
      rating: 4.9,
      reviews: 203,
      students: 345,
      price: 4999,
      originalPrice: 6499,
      image: '/api/placeholder/400/250',
      features: ['Live Classes', 'Group Practice', 'Performance Training', 'Certificate'],
      preview: true,
      popular: false,
      new: false
    },
    {
      id: 5,
      title: 'Mridangam Basics',
      instructor: 'Pt. Zakir Hussain',
      description: 'Learn the fundamentals of mridangam with traditional South Indian techniques',
      category: 'Percussion',
      level: 'Beginner',
      duration: '3 months',
      totalLessons: 20,
      rating: 4.8,
      reviews: 167,
      students: 523,
      price: 3999,
      originalPrice: 5499,
      image: '/api/placeholder/400/250',
      features: ['Live Classes', 'Rhythm Practice', 'Traditional Compositions', 'Certificate'],
      preview: true,
      popular: true,
      new: true
    },
    {
      id: 6,
      title: 'Music Theory Fundamentals',
      instructor: 'Dr. Ashwini Bhide',
      description: 'Understand the theoretical foundations of Indian classical music',
      category: 'Music Theory',
      level: 'Beginner',
      duration: '2 months',
      totalLessons: 12,
      rating: 4.6,
      reviews: 98,
      students: 789,
      price: 1999,
      originalPrice: 2999,
      image: '/api/placeholder/400/250',
      features: ['Video Lessons', 'Interactive Exercises', 'Study Materials', 'Certificate'],
      preview: true,
      popular: false,
      new: false
    }
  ];

  const filteredCourses = availableCourses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || 
                           course.category.toLowerCase() === selectedCategory.toLowerCase();
    
    const matchesLevel = selectedLevel === 'all' || 
                        course.level.toLowerCase() === selectedLevel.toLowerCase();
    
    return matchesSearch && matchesCategory && matchesLevel;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.students - a.students;
      case 'rating':
        return b.rating - a.rating;
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'newest':
        return b.new - a.new;
      default:
        return 0;
    }
  });

  const toggleFavorite = (courseId) => {
    setFavorites(prev => 
      prev.includes(courseId) 
        ? prev.filter(id => id !== courseId)
        : [...prev, courseId]
    );
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'Beginner': return 'text-green-600 bg-green-100';
      case 'Intermediate': return 'text-blue-600 bg-blue-100';
      case 'Advanced': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Explore Courses</h1>
        <p className="text-gray-600 text-xl">Discover new courses and expand your musical knowledge</p>
      </div>

      {/* Search and Filters */}
      <div className="dashboard-card mb-6">
        <div className="dashboard-card-content">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="lg:col-span-2 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search courses, instructors, or topics..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <div className="relative">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Categories</option>
                {categories.slice(1).map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>

            {/* Level Filter */}
            <div className="relative">
              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                {levels.map(level => (
                  <option key={level} value={level === 'All Levels' ? 'all' : level}>{level}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>

            {/* Sort */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                <option value="popular">Most Popular</option>
                <option value="rating">Highest Rated</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="newest">Newest</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">
          Showing {filteredCourses.length} course{filteredCourses.length !== 1 ? 's' : ''}
          {searchTerm && ` for "${searchTerm}"`}
        </p>
      </div>

      {/* Courses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCourses.map((course) => (
          <div key={course.id} className="dashboard-card group hover:shadow-lg transition-shadow">
            {/* Course Image */}
            <div className="relative">
              <div className="aspect-video bg-gradient-to-br from-music-background-orange/20 to-music-background/20 rounded-t-xl flex items-center justify-center">
                <BookOpen size={48} className="text-music-background-orange" />
              </div>
              
              {/* Badges */}
              <div className="absolute top-2 left-2 flex gap-2">
                {course.popular && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded">Popular</span>
                )}
                {course.new && (
                  <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">New</span>
                )}
              </div>

              {/* Favorite Button */}
              <button
                onClick={() => toggleFavorite(course.id)}
                className="absolute top-2 right-2 p-2 bg-white bg-opacity-80 rounded-full hover:bg-opacity-100 transition-all"
              >
                <Heart 
                  size={16} 
                  className={favorites.includes(course.id) ? 'text-red-500 fill-current' : 'text-gray-600'} 
                />
              </button>

              {/* Preview Button */}
              {course.preview && (
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center rounded-t-xl">
                  <button className="bg-white bg-opacity-0 group-hover:bg-opacity-90 text-music-background-orange p-3 rounded-full transition-all duration-200 transform scale-0 group-hover:scale-100">
                    <Play size={24} fill="currentColor" />
                  </button>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="dashboard-card-content">
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-semibold text-gray-800 line-clamp-2">{course.title}</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
                  {course.level}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">by {course.instructor}</p>
              <p className="text-sm text-gray-500 mb-3 line-clamp-2">{course.description}</p>

              {/* Stats */}
              <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                <div className="flex items-center gap-1">
                  <Star size={14} className="text-yellow-500" fill="currentColor" />
                  <span>{course.rating}</span>
                  <span>({course.reviews})</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users size={14} />
                  <span>{course.students}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock size={14} />
                  <span>{course.duration}</span>
                </div>
              </div>

              {/* Features */}
              <div className="flex flex-wrap gap-1 mb-4">
                {course.features.slice(0, 3).map((feature, index) => (
                  <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {feature}
                  </span>
                ))}
                {course.features.length > 3 && (
                  <span className="text-xs text-gray-500">+{course.features.length - 3} more</span>
                )}
              </div>

              {/* Price and Action */}
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-lg font-bold text-gray-800">₹{course.price}</span>
                  {course.originalPrice > course.price && (
                    <span className="text-sm text-gray-500 line-through ml-2">₹{course.originalPrice}</span>
                  )}
                </div>
                <button className="bg-music-background-orange hover:bg-music-background text-white px-4 py-2 rounded-lg font-medium transition-colors">
                  Enroll Now
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <div className="text-center py-12">
          <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No courses found</h3>
          <p className="text-gray-500">
            Try adjusting your search or filter criteria to find more courses
          </p>
        </div>
      )}
    </div>
  );
};

export default ExploreCourses;
