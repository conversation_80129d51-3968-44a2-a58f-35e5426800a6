import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  BookOpen,
  Users,
  Calendar,
  Music,
  Settings,
  User,
  BarChart3,
  FileText,
  Bell,
  ChevronDown,
  ChevronRight,
  LogOut,
  X
} from 'lucide-react';
import logo from '../../assets/logo.png';

const Sidebar = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState({});

  const toggleSubmenu = (menuKey) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  // Auto-expand menu if current route is in submenu
  const isMenuExpanded = (item) => {
    if (expandedMenus[item.key] !== undefined) {
      return expandedMenus[item.key];
    }
    // Auto-expand if current route matches any submenu item
    if (item.submenu) {
      return item.submenu.some(subItem => location.pathname === subItem.path);
    }
    return false;
  };

  const menuItems = [
    {
      key: 'dashboard',
      title: 'Dashboard',
      icon: Home,
      path: '/dashboard',
      badge: null
    },
    {
      key: 'courses',
      title: 'Courses',
      icon: BookOpen,
      submenu: [
        { title: 'All Courses', path: '/dashboard/courses' },
        { title: 'Add Course', path: '/dashboard/courses/add' },
        // { title: 'Categories', path: '/dashboard/courses/categories' }
      ]
    },
    {
      key: 'students',
      title: 'Students',
      icon: Users,
      submenu: [
        { title: 'All Students', path: '/dashboard/students' },
        { title: 'Add Student', path: '/dashboard/students/add' },
        { title: 'Student Groups', path: '/dashboard/students/groups' }
      ]
    },
    {
      key: 'schedule',
      title: 'Schedule',
      icon: Calendar,
      path: '/dashboard/schedule',
      badge: '3'
    },
    {
      key: 'instruments',
      title: 'Instruments',
      icon: Music,
      submenu: [
        { title: 'All Instruments', path: '/dashboard/instruments' },
        { title: 'Add Instrument', path: '/dashboard/instruments/add' },
        { title: 'Maintenance', path: '/dashboard/instruments/maintenance' }
      ]
    },
    {
      key: 'reports',
      title: 'Reports',
      icon: BarChart3,
      submenu: [
        { title: 'Student Progress', path: '/dashboard/reports/progress' },
        { title: 'Attendance', path: '/dashboard/reports/attendance' }
      ]
    },
    {
      key: 'notifications',
      title: 'Notifications',
      icon: Bell,
      path: '/dashboard/notifications',
      badge: '12'
    },
    {
      key: 'Payments',
      title: 'Payments',
      icon: FileText,
      path: '/dashboard/payments',
      badge: '12',
       submenu: [
        { title: 'Payments History', path: '/dashboard/paymentHistory' },
        { title: 'Financial Reports', path: '/dashboard/reports/financial' },
      ]
    }
  ];

  const isActiveRoute = (path) => {
    return location.pathname === path;
  };

  const isActiveSubmenu = (submenu) => {
    return submenu.some(item => location.pathname === item.path);
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
        />
      )}
 
      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-screen bg-music-background border-r border-music-background-orange/20 z-50 sidebar-transition
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:fixed lg:z-auto
        w-64 flex flex-col shadow-lg
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-music-background-orange/20 bg-music-background/50">
          <div className="flex items-center gap-3">
            <img src={logo} alt="SwaRas Academy" className=" rounded-lg shadow-md" />
           
          </div>
          <button
            onClick={toggleSidebar}
            className="lg:hidden text-music-light hover:text-music-background-light p-2 rounded-lg hover:bg-music-background-orange/20 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4 sidebar-scroll">
          <div className="px-4 space-y-1">
            {menuItems.map((item) => (
              <div key={item.key}>
                {item.submenu ? (
                  <div>
                    <button
                      onClick={() => toggleSubmenu(item.key)}
                      className={`
                        sidebar-menu-item w-full
                        ${isActiveSubmenu(item.submenu)
                          ? 'sidebar-menu-item-active'
                          : 'sidebar-menu-item-inactive'
                        }
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <item.icon size={18} />
                        <span>{item.title}</span>
                      </div>
                      {isMenuExpanded(item) ? (
                        <ChevronDown size={16} />
                      ) : (
                        <ChevronRight size={16} />
                      )}
                    </button>

                    {isMenuExpanded(item) && (
                      <div className="ml-6 mt-1 space-y-1">
                        {item.submenu.map((subItem) => (
                          <Link
                            key={subItem.path}
                            to={subItem.path}
                            className={`
                              sidebar-submenu-item
                              ${isActiveRoute(subItem.path)
                                ? 'sidebar-submenu-item-active'
                                : 'sidebar-submenu-item-inactive'
                              }
                            `}
                          >
                            {subItem.title}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={`
                      sidebar-menu-item
                      ${isActiveRoute(item.path)
                        ? 'sidebar-menu-item-active'
                        : 'sidebar-menu-item-inactive'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <item.icon size={18} />
                      <span>{item.title}</span>
                    </div>
                    {item.badge && (
                      <span className="bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                )}
              </div>
            ))}
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-music-background-orange/20">
          <div className="space-y-1">
            <Link
              to="/dashboard/profile"
              className="flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-music-light hover:bg-music-background-orange/20 hover:text-music-background-light transition-colors"
            >
              <User size={18} />
              <span>Profile</span>
            </Link>
            <Link
              to="/dashboard/settings"
              className="flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-music-light hover:bg-music-background-orange/20 hover:text-music-background-light transition-colors"
            >
              <Settings size={18} />
              <span>Settings</span>
            </Link>
            <button className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-music-light hover:bg-red-500/20 hover:text-red-400 transition-colors">
              <LogOut size={18} />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;