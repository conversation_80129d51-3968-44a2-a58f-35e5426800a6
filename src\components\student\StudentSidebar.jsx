import { useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { 
  Home, 
  BookOpen, 
  Video, 
  GraduationCap,
  Library,
  FileText,
  User,
  LogOut,
  ChevronDown,
  ChevronRight,
  X,
  Calendar
} from 'lucide-react';

const StudentSidebar = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState({});

  const toggleSubmenu = (key) => {
    setExpandedMenus(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const menuItems = [
    {
      key: 'dashboard',
      title: 'Dashboard',
      icon: Home,
      path: '/student/dashboard',
      badge: null
    },
    {
      key: 'classes',
      title: 'Classes',
      icon: Calendar,
      path: '/student/classes',
      badge: '2'
    },
    {
      key: 'recordings',
      title: 'Class Recordings',
      icon: Video,
      path: '/student/recordings',
      badge: null
    },
    {
      key: 'courses',
      title: 'Courses',
      icon: GraduationCap,
      submenu: [
        { title: 'Active Courses', path: '/student/courses/active' },
        { title: 'New Courses', path: '/student/courses/explore' }
      ]
    },
    {
      key: 'library',
      title: 'Library',
      icon: Library,
      path: '/student/library',
      badge: null
    },
    {
      key: 'assignments',
      title: 'Assignments',
      icon: FileText,
      path: '/student/assignments',
      badge: '3'
    },
    {
      key: 'profile',
      title: 'Profile',
      icon: User,
      path: '/student/profile',
      badge: null
    }
  ];

  const isActiveRoute = (path) => {
    return location.pathname === path;
  };

  const isActiveSubmenu = (submenu) => {
    return submenu.some(item => location.pathname === item.path);
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:z-auto
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* Logo Section */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-music-background-orange rounded-lg flex items-center justify-center">
              <BookOpen className="text-white" size={18} />
            </div>
            <div>
              <h2 className="font-bold text-gray-800 text-lg">SwaRas</h2>
              <p className="text-xs text-gray-500">Student Portal</p>
            </div>
          </div>
          <button 
            onClick={toggleSidebar}
            className="lg:hidden p-1 rounded-lg hover:bg-gray-100"
          >
            <X size={20} />
          </button>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {menuItems.map((item) => (
            <div key={item.key}>
              {item.submenu ? (
                // Menu with submenu
                <div>
                  <button
                    onClick={() => toggleSubmenu(item.key)}
                    className={`
                      sidebar-menu-item w-full
                      ${isActiveSubmenu(item.submenu) 
                        ? 'sidebar-menu-item-active' 
                        : 'sidebar-menu-item-inactive'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <item.icon size={18} />
                      <span>{item.title}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {item.badge && (
                        <span className="bg-music-background-orange text-white text-xs px-2 py-0.5 rounded-full">
                          {item.badge}
                        </span>
                      )}
                      {expandedMenus[item.key] ? (
                        <ChevronDown size={16} />
                      ) : (
                        <ChevronRight size={16} />
                      )}
                    </div>
                  </button>
                  
                  {/* Submenu */}
                  {expandedMenus[item.key] && (
                    <div className="ml-6 mt-2 space-y-1">
                      {item.submenu.map((subItem, index) => (
                        <Link
                          key={index}
                          to={subItem.path}
                          className={`
                            block px-3 py-2 rounded-lg text-sm transition-all duration-200
                            ${isActiveRoute(subItem.path)
                              ? 'bg-music-background-orange/20 text-music-background-orange font-medium'
                              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
                            }
                          `}
                        >
                          {subItem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                // Regular menu item
                <Link
                  to={item.path}
                  className={`
                    sidebar-menu-item
                    ${isActiveRoute(item.path) 
                      ? 'sidebar-menu-item-active' 
                      : 'sidebar-menu-item-inactive'
                    }
                  `}
                >
                  <div className="flex items-center gap-3">
                    <item.icon size={18} />
                    <span>{item.title}</span>
                  </div>
                  {item.badge && (
                    <span className="bg-music-background-orange text-white text-xs px-2 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
              )}
            </div>
          ))}
        </nav>

        {/* Logout Section */}
        <div className="p-4 border-t border-gray-200">
          <button className="sidebar-menu-item w-full text-red-600 hover:bg-red-50">
            <div className="flex items-center gap-3">
              <LogOut size={18} />
              <span>Logout</span>
            </div>
          </button>
        </div>
      </div>
    </>
  );
};

export default StudentSidebar;
