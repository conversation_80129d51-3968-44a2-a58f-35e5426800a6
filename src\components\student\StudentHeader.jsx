import { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { 
  Menu, 
  Search, 
  Bell, 
  User, 
  Settings, 
  LogOut,
  BookOpen,
  Calendar,
  Clock
} from 'lucide-react';

const StudentHeader = ({ toggleSidebar }) => {
  const location = useLocation();

  // Get page title based on current route
  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/student/dashboard') return 'Dashboard';
    if (path === '/student/classes') return 'Classes';
    if (path === '/student/recordings') return 'Class Recordings';
    if (path === '/student/courses/active') return 'Active Courses';
    if (path === '/student/courses/explore') return 'Explore Courses';
    if (path === '/student/library') return 'Library';
    if (path === '/student/assignments') return 'Assignments';
    if (path === '/student/profile') return 'Profile';
    return 'Student Dashboard';
  };

  const getPageDescription = () => {
    const path = location.pathname;
    if (path === '/student/dashboard') return 'Track your learning progress and upcoming activities';
    if (path === '/student/classes') return 'Join your scheduled classes and view upcoming sessions';
    if (path === '/student/recordings') return 'Access recordings of your previous classes';
    if (path === '/student/courses/active') return 'Continue with your enrolled courses';
    if (path === '/student/courses/explore') return 'Discover and enroll in new courses';
    if (path === '/student/library') return 'Access learning materials and resources';
    if (path === '/student/assignments') return 'View and submit your assignments';
    if (path === '/student/profile') return 'Manage your profile and payment information';
    return 'Welcome to your learning portal';
  };

  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  const notifications = [
    { id: 1, title: 'New assignment available', time: '10 min ago', unread: true },
    { id: 2, title: 'Class starting in 30 minutes', time: '20 min ago', unread: true },
    { id: 3, title: 'New course material uploaded', time: '2 hours ago', unread: false },
  ];

  const upcomingClasses = [
    { id: 1, title: 'Tabla Basics', time: '2:00 PM', instructor: 'Guru Ramesh' },
    { id: 2, title: 'Vocal Training', time: '4:30 PM', instructor: 'Smt. Priya' }
  ];

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4 shadow-sm sticky top-0 z-40">
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          <button
            onClick={toggleSidebar}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Menu size={20} />
          </button>
          
          <div className="hidden md:block">
            <h1 className="text-xl font-semibold text-gray-800">{getPageTitle()}</h1>
            <p className="text-sm text-gray-500">{getPageDescription()}</p>
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="hidden md:flex flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              placeholder="Search courses, assignments, materials..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-4">
          {/* Quick Access - Upcoming Classes */}
          <div className="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg">
            <Clock size={16} className="text-blue-600" />
            <span className="text-sm text-blue-700 font-medium">
              Next class: {upcomingClasses[0]?.time || 'No classes today'}
            </span>
          </div>

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <Bell size={20} />
              {notifications.filter(n => n.unread).length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {notifications.filter(n => n.unread).length}
                </span>
              )}
            </button>

            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="font-semibold text-gray-800">Notifications</h3>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${
                        notification.unread ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          notification.unread ? 'bg-blue-500' : 'bg-gray-300'
                        }`} />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-800">
                            {notification.title}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {notification.time}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-3 border-t border-gray-200">
                  <button className="text-sm text-music-background-orange hover:text-music-background font-medium">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Profile Menu */}
          <div className="relative">
            <button
              onClick={() => setShowProfileMenu(!showProfileMenu)}
              className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="w-8 h-8 bg-music-background-orange rounded-full flex items-center justify-center">
                <User size={16} className="text-white" />
              </div>
              <div className="hidden md:block text-left">
                <p className="text-sm font-medium text-gray-800">Student Name</p>
                <p className="text-xs text-gray-500">Active Student</p>
              </div>
            </button>

            {showProfileMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-2">
                  <a
                    href="/student/profile"
                    className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
                  >
                    <User size={16} />
                    Profile
                  </a>
                  <a
                    href="/student/settings"
                    className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
                  >
                    <Settings size={16} />
                    Settings
                  </a>
                  <hr className="my-2" />
                  <button className="flex items-center gap-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg w-full text-left">
                    <LogOut size={16} />
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default StudentHeader;
