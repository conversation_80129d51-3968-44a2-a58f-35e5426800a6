import { useState } from 'react';
import { 
  FileText, 
  Video, 
  Music, 
  Download,
  Search,
  Filter,
  Eye,
  Calendar,
  ChevronDown,
  BookOpen,
  Headphones,
  Image,
  Archive
} from 'lucide-react';

const Library = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [sortBy, setSortBy] = useState('recent');

  const resourceTypes = [
    { value: 'all', label: 'All Resources', icon: Archive },
    { value: 'pdf', label: 'PDFs', icon: FileText },
    { value: 'video', label: 'Videos', icon: Video },
    { value: 'audio', label: 'Audio', icon: Headphones },
    { value: 'image', label: 'Images', icon: Image }
  ];

  const courses = [
    'All Courses',
    'Tabla Fundamentals',
    'Classical Vocal Training',
    'Sitar Intermediate',
    'Harmonium Basics'
  ];

  const libraryResources = [
    {
      id: 1,
      title: 'Tabla Hand Positions Guide',
      type: 'pdf',
      course: 'Tabla Fundamentals',
      instructor: '<PERSON>',
      description: 'Comprehensive guide to proper hand positions and striking techniques',
      size: '2.4 MB',
      pages: 24,
      uploadDate: '2024-12-15',
      downloads: 156,
      category: 'Study Material',
      tags: ['Technique', 'Beginner', 'Reference']
    },
    {
      id: 2,
      title: 'Breathing Exercises for Vocal Training',
      type: 'video',
      course: 'Classical Vocal Training',
      instructor: 'Smt. Priya Sharma',
      description: 'Step-by-step breathing exercises to improve vocal control',
      duration: '15:32',
      uploadDate: '2024-12-14',
      views: 89,
      category: 'Practice Video',
      tags: ['Breathing', 'Vocal', 'Exercise']
    },
    {
      id: 3,
      title: 'Raga Yaman Practice Audio',
      type: 'audio',
      course: 'Classical Vocal Training',
      instructor: 'Smt. Priya Sharma',
      description: 'Practice along with this Raga Yaman demonstration',
      duration: '8:45',
      size: '12.3 MB',
      uploadDate: '2024-12-13',
      downloads: 67,
      category: 'Practice Audio',
      tags: ['Raga', 'Practice', 'Classical']
    },
    {
      id: 4,
      title: 'Sitar Tuning Chart',
      type: 'image',
      course: 'Sitar Intermediate',
      instructor: 'Pt. Ravi Shankar',
      description: 'Visual guide for proper sitar tuning',
      size: '1.8 MB',
      uploadDate: '2024-12-12',
      downloads: 45,
      category: 'Reference',
      tags: ['Tuning', 'Reference', 'Sitar']
    },
    {
      id: 5,
      title: 'Basic Rhythm Patterns',
      type: 'pdf',
      course: 'Tabla Fundamentals',
      instructor: 'Guru Ramesh Kumar',
      description: 'Collection of fundamental rhythm patterns with notation',
      size: '3.1 MB',
      pages: 18,
      uploadDate: '2024-12-11',
      downloads: 123,
      category: 'Study Material',
      tags: ['Rhythm', 'Notation', 'Practice']
    },
    {
      id: 6,
      title: 'Finger Exercise Demonstration',
      type: 'video',
      course: 'Sitar Intermediate',
      instructor: 'Pt. Ravi Shankar',
      description: 'Detailed demonstration of essential finger exercises',
      duration: '22:18',
      uploadDate: '2024-12-10',
      views: 34,
      category: 'Practice Video',
      tags: ['Technique', 'Exercises', 'Fingers']
    },
    {
      id: 7,
      title: 'Harmonium Scale Practice',
      type: 'audio',
      course: 'Harmonium Basics',
      instructor: 'Pt. Suresh Wadkar',
      description: 'Practice scales with harmonium accompaniment',
      duration: '12:30',
      size: '18.7 MB',
      uploadDate: '2024-12-09',
      downloads: 78,
      category: 'Practice Audio',
      tags: ['Scales', 'Harmonium', 'Practice']
    },
    {
      id: 8,
      title: 'Music Theory Fundamentals',
      type: 'pdf',
      course: 'All Courses',
      instructor: 'Dr. Ashwini Bhide',
      description: 'Essential music theory concepts for Indian classical music',
      size: '4.2 MB',
      pages: 32,
      uploadDate: '2024-12-08',
      downloads: 234,
      category: 'Study Material',
      tags: ['Theory', 'Fundamentals', 'Reference']
    }
  ];

  const filteredResources = libraryResources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = selectedType === 'all' || resource.type === selectedType;
    const matchesCourse = selectedCourse === 'all' || resource.course === selectedCourse;
    
    return matchesSearch && matchesType && matchesCourse;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.uploadDate) - new Date(a.uploadDate);
      case 'oldest':
        return new Date(a.uploadDate) - new Date(b.uploadDate);
      case 'downloads':
        return (b.downloads || 0) - (a.downloads || 0);
      case 'views':
        return (b.views || 0) - (a.views || 0);
      case 'name':
        return a.title.localeCompare(b.title);
      default:
        return 0;
    }
  });

  const getResourceIcon = (type) => {
    switch (type) {
      case 'pdf': return FileText;
      case 'video': return Video;
      case 'audio': return Headphones;
      case 'image': return Image;
      default: return FileText;
    }
  };

  const getResourceColor = (type) => {
    switch (type) {
      case 'pdf': return 'text-red-600 bg-red-100';
      case 'video': return 'text-blue-600 bg-blue-100';
      case 'audio': return 'text-green-600 bg-green-100';
      case 'image': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Library</h1>
        <p className="text-gray-600 text-xl">Access your learning materials and resources</p>
      </div>

      {/* Resource Type Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {resourceTypes.map((type) => {
          const Icon = type.icon;
          return (
            <button
              key={type.value}
              onClick={() => setSelectedType(type.value)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedType === type.value
                  ? 'bg-music-background-orange text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <Icon size={16} />
              {type.label}
            </button>
          );
        })}
      </div>

      {/* Search and Filters */}
      <div className="dashboard-card mb-6">
        <div className="dashboard-card-content">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="md:col-span-2 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search resources, topics, or tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
              />
            </div>

            {/* Course Filter */}
            <div className="relative">
              <select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                {courses.map(course => (
                  <option key={course} value={course === 'All Courses' ? 'all' : course}>{course}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>

            {/* Sort */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                <option value="recent">Most Recent</option>
                <option value="oldest">Oldest First</option>
                <option value="downloads">Most Downloaded</option>
                <option value="views">Most Viewed</option>
                <option value="name">Name A-Z</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">
          Showing {filteredResources.length} resource{filteredResources.length !== 1 ? 's' : ''}
          {searchTerm && ` for "${searchTerm}"`}
        </p>
      </div>

      {/* Resources List */}
      <div className="space-y-4">
        {filteredResources.map((resource) => {
          const ResourceIcon = getResourceIcon(resource.type);
          return (
            <div key={resource.id} className="dashboard-card hover:shadow-md transition-shadow">
              <div className="dashboard-card-content">
                <div className="flex items-start gap-4">
                  {/* Resource Icon */}
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getResourceColor(resource.type)}`}>
                    <ResourceIcon size={24} />
                  </div>

                  {/* Resource Info */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-gray-800 mb-1">{resource.title}</h3>
                        <p className="text-sm text-gray-600">
                          {resource.course} • by {resource.instructor}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getResourceColor(resource.type)}`}>
                        {resource.type.toUpperCase()}
                      </span>
                    </div>

                    <p className="text-sm text-gray-600 mb-3">{resource.description}</p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mb-3">
                      {resource.tags.map((tag, index) => (
                        <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Resource Details */}
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar size={14} />
                        <span>{formatDate(resource.uploadDate)}</span>
                      </div>
                      {resource.size && (
                        <span>Size: {resource.size}</span>
                      )}
                      {resource.pages && (
                        <span>{resource.pages} pages</span>
                      )}
                      {resource.duration && (
                        <span>Duration: {resource.duration}</span>
                      )}
                      {resource.downloads && (
                        <div className="flex items-center gap-1">
                          <Download size={14} />
                          <span>{resource.downloads} downloads</span>
                        </div>
                      )}
                      {resource.views && (
                        <div className="flex items-center gap-1">
                          <Eye size={14} />
                          <span>{resource.views} views</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2">
                    {resource.type === 'video' || resource.type === 'audio' ? (
                      <button className="bg-music-background-orange hover:bg-music-background text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                        <Eye size={16} />
                        View
                      </button>
                    ) : (
                      <button className="bg-music-background-orange hover:bg-music-background text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                        <Download size={16} />
                        Download
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredResources.length === 0 && (
        <div className="text-center py-12">
          <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No resources found</h3>
          <p className="text-gray-500">
            {searchTerm || selectedType !== 'all' || selectedCourse !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'No learning resources are available at the moment'
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default Library;
