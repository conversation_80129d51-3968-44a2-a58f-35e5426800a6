import { 
  Users, 
  BookOpen, 
  Calendar, 
  DollarSign,
  TrendingUp,
  Music,
  Clock,
  Award
} from 'lucide-react';
import { <PERSON> } from 'react-router-dom';

const Dashboard = () => {
  const stats = [
    {
      title: 'Total Students',
      value: '248',
      change: '+12%',
      changeType: 'positive',
      icon: Users,
      color: 'bg-blue-500'
    },
    {
      title: 'Active Courses',
      value: '32',
      change: '+3',
      changeType: 'positive',
      icon: BookOpen,
      color: 'bg-green-500'
    },
    {
      title: 'This Month Revenue',
      value: '$12,450',
      change: '+8.2%',
      changeType: 'positive',
      icon: DollarSign,
      color: 'bg-music-background-orange'
    },
    {
      title: 'Scheduled Classes',
      value: '156',
      change: '+24',
      changeType: 'positive',
      icon: Calendar,
      color: 'bg-purple-500'
    }
  ];

  const recentActivities = [
    { id: 1, type: 'enrollment', message: 'New student enrolled in Tabla Basics', time: '2 hours ago' },
    { id: 2, type: 'payment', message: 'Payment received from <PERSON>', time: '4 hours ago' },
    { id: 3, type: 'class', message: 'Sitar Advanced class completed', time: '6 hours ago' },
    { id: 4, type: 'achievement', message: 'Student completed Harmonium Level 1', time: '1 day ago' },
  ];

  const upcomingClasses = [
    { id: 1, course: 'Tabla Basics', instructor: 'Guru Ramesh', time: '10:00 AM', students: 12 },
    { id: 2, course: 'Sitar Intermediate', instructor: 'Guru Priya', time: '2:00 PM', students: 8 },
    { id: 3, course: 'Vocal Training', instructor: 'Guru Anita', time: '4:00 PM', students: 15 },
    { id: 4, course: 'Harmonium Advanced', instructor: 'Guru Suresh', time: '6:00 PM', students: 6 },
  ];

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header">
        <h1 className="text-4xl font-bold text-gray-900 mb-3">Dashboard Overview</h1>
        <p className="text-gray-600 text-xl">Welcome back! Here's what's happening at SwaRas Academy today.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="dashboard-card stats-card p-6 bg-gradient-to-br from-white to-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</p>
                <div className="flex items-center">
                  <TrendingUp size={16} className={`${stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'} mr-1`} />
                  <span className={`text-sm font-medium ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">from last month</span>
                </div>
              </div>
              <div className={`${stat.color} p-4 rounded-xl shadow-lg`}>
                <stat.icon size={28} className="text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <div className="dashboard-card">
          <div className="dashboard-card-header">
            <h2 className="text-lg font-semibold text-gray-900">Recent Activities</h2>
          </div>
          <div className="dashboard-card-content">
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    {activity.type === 'enrollment' && <Users size={16} className="text-blue-500 mt-1" />}
                    {activity.type === 'payment' && <DollarSign size={16} className="text-green-500 mt-1" />}
                    {activity.type === 'class' && <Music size={16} className="text-purple-500 mt-1" />}
                    {activity.type === 'achievement' && <Award size={16} className="text-orange-500 mt-1" />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Upcoming Classes */}
        <div className="dashboard-card">
          <div className="dashboard-card-header">
            <h2 className="text-lg font-semibold text-gray-900">Today's Classes</h2>
          </div>
          <div className="dashboard-card-content">
            <div className="space-y-4">
              {upcomingClasses.map((classItem) => (
                <div key={classItem.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="bg-music-background-orange p-2 rounded-lg">
                      <Music size={16} className="text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{classItem.course}</p>
                      <p className="text-xs text-gray-500">{classItem.instructor}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Clock size={14} />
                      {classItem.time}
                    </div>
                    <p className="text-xs text-gray-500">{classItem.students} students</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="dashboard-card p-8 mb-12 bg-gradient-to-r from-white to-gray-50">
        <h2 className="text-2xl font-bold text-gray-900 mb-8">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <button className="group bg-white border-2 border-gray-200 hover:border-blue-300 rounded-xl p-8 text-left transition-all duration-200 hover:shadow-lg hover:scale-105">
            <div className="flex items-center gap-4">
              <div className="bg-blue-100 group-hover:bg-blue-200 p-4 rounded-xl transition-colors">
                <Users className="text-blue-600" size={28} />
              </div>
              <div>
                <span className="font-bold text-xl block text-gray-900">Add New Student</span>
                <span className="text-gray-500 mt-1">Enroll a new student in the academy</span>
              </div>
            </div>
          </button>
          <button className="group bg-white border-2 border-gray-200 hover:border-green-300 rounded-xl p-8 text-left transition-all duration-200 hover:shadow-lg hover:scale-105">
            <div className="flex items-center gap-4">
              <div className="bg-green-100 group-hover:bg-green-200 p-4 rounded-xl transition-colors">
                <BookOpen className="text-green-600" size={28} />
              </div>
              <div>
                <Link to="/dashboard/courses/add" className="font-bold text-xl block text-gray-900">Create Course</Link>
      
                <span className="text-gray-500 mt-1">Add a new music course</span>
              </div>
            </div>
          </button>
          <button className="group bg-white border-2 border-gray-200 hover:border-purple-300 rounded-xl p-8 text-left transition-all duration-200 hover:shadow-lg hover:scale-105">
            <div className="flex items-center gap-4">
              <div className="bg-purple-100 group-hover:bg-purple-200 p-4 rounded-xl transition-colors">
                <Calendar className="text-purple-600" size={28} />
              </div>
              <div>
                <Link to="/dashboard/schedule" className="font-bold text-xl block text-gray-900">Schedule Class</Link>
                <span className="text-gray-500 mt-1">Plan a new class session</span>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
