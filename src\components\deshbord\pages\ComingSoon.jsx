import { Construction, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const ComingSoon = ({ title = "Coming Soon", description = "This feature is under development" }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
      <div className="bg-music-background-orange/10 p-8 rounded-full mb-6">
        <Construction size={64} className="text-music-background-orange" />
      </div>
      
      <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
      <p className="text-lg text-gray-600 mb-8 max-w-md">
        {description}. We're working hard to bring you this feature soon!
      </p>
      
      <Link 
        to="/dashboard" 
        className="flex items-center gap-2 bg-music-background-orange text-white px-6 py-3 rounded-lg hover:bg-music-background-orange/90 transition-colors"
      >
        <ArrowLeft size={20} />
        Back to Dashboard
      </Link>
    </div>
  );
};

export default ComingSoon;
