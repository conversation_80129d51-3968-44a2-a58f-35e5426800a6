import { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  Video, 
  Users, 
  MapPin,
  Play,
  CheckCircle,
  AlertCircle,
  Filter,
  Search
} from 'lucide-react';

const Classes = () => {
  const [activeTab, setActiveTab] = useState('upcoming');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const upcomingClasses = [
    {
      id: 1,
      title: 'Tabla Fundamentals - Rhythm Patterns',
      instructor: '<PERSON>',
      course: 'Tabla Fundamentals',
      date: '2024-12-18',
      time: '14:00 - 15:00',
      type: 'Live Class',
      location: 'Online',
      status: 'scheduled',
      canJoin: true,
      joinLink: '#',
      description: 'Learn basic rhythm patterns and hand techniques',
      materials: ['Practice Guide PDF', 'Audio Examples']
    },
    {
      id: 2,
      title: 'Vocal Training - Breathing Techniques',
      instructor: '<PERSON>mt. <PERSON><PERSON>',
      course: 'Classical Vocal Training',
      date: '2024-12-18',
      time: '16:30 - 17:30',
      type: 'Workshop',
      location: 'Online',
      status: 'scheduled',
      canJoin: false,
      joinLink: '#',
      description: 'Master proper breathing techniques for classical singing',
      materials: ['Breathing Exercise Guide', 'Practice Audio']
    },
    {
      id: 3,
      title: 'Sitar Theory - Raga Introduction',
      instructor: 'Pt. Ravi Shankar',
      course: 'Sitar Intermediate',
      date: '2024-12-19',
      time: '15:00 - 16:00',
      type: 'Theory',
      location: 'Online',
      status: 'scheduled',
      canJoin: false,
      joinLink: '#',
      description: 'Introduction to major ragas and their characteristics',
      materials: ['Raga Chart', 'Audio Examples', 'Practice Exercises']
    },
    {
      id: 4,
      title: 'Group Practice Session',
      instructor: 'Multiple Instructors',
      course: 'All Courses',
      date: '2024-12-20',
      time: '18:00 - 19:00',
      type: 'Practice',
      location: 'Online',
      status: 'scheduled',
      canJoin: false,
      joinLink: '#',
      description: 'Collaborative practice session with fellow students',
      materials: ['Session Guidelines']
    }
  ];

  const pastClasses = [
    {
      id: 5,
      title: 'Tabla Basics - Hand Positions',
      instructor: 'Guru Ramesh Kumar',
      course: 'Tabla Fundamentals',
      date: '2024-12-15',
      time: '14:00 - 15:00',
      type: 'Live Class',
      status: 'completed',
      attended: true,
      recording: true
    },
    {
      id: 6,
      title: 'Vocal Warm-up Techniques',
      instructor: 'Smt. Priya Sharma',
      course: 'Classical Vocal Training',
      date: '2024-12-14',
      time: '16:30 - 17:30',
      type: 'Workshop',
      status: 'completed',
      attended: true,
      recording: true
    },
    {
      id: 7,
      title: 'Sitar Tuning Workshop',
      instructor: 'Pt. Ravi Shankar',
      course: 'Sitar Intermediate',
      date: '2024-12-13',
      time: '15:00 - 16:00',
      type: 'Workshop',
      status: 'completed',
      attended: false,
      recording: true
    }
  ];

  const getStatusColor = (status, canJoin = false) => {
    if (canJoin) return 'text-green-600 bg-green-100';
    switch (status) {
      case 'scheduled': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-gray-600 bg-gray-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'Live Class': return 'text-green-600 bg-green-100';
      case 'Workshop': return 'text-purple-600 bg-purple-100';
      case 'Theory': return 'text-blue-600 bg-blue-100';
      case 'Practice': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const filteredClasses = (classes) => {
    return classes.filter(classItem => {
      const matchesSearch = classItem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           classItem.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           classItem.course.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesFilter = filterType === 'all' || classItem.type.toLowerCase().replace(' ', '') === filterType;
      
      return matchesSearch && matchesFilter;
    });
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Classes</h1>
          <p className="text-gray-600 text-xl">Join your scheduled classes and view class history</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6 w-fit">
        <button
          onClick={() => setActiveTab('upcoming')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'upcoming'
              ? 'bg-white text-music-background-orange shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          Upcoming Classes
        </button>
        <button
          onClick={() => setActiveTab('past')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'past'
              ? 'bg-white text-music-background-orange shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          Past Classes
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <input
            type="text"
            placeholder="Search classes, instructors, or courses..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
          >
            <option value="all">All Types</option>
            <option value="liveclass">Live Class</option>
            <option value="workshop">Workshop</option>
            <option value="theory">Theory</option>
            <option value="practice">Practice</option>
          </select>
        </div>
      </div>

      {/* Classes List */}
      <div className="space-y-4">
        {activeTab === 'upcoming' && (
          <>
            {filteredClasses(upcomingClasses).map((classItem) => (
              <div key={classItem.id} className="dashboard-card">
                <div className="dashboard-card-content">
                  <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-800 mb-1">{classItem.title}</h3>
                          <p className="text-gray-600">by {classItem.instructor}</p>
                          <p className="text-sm text-gray-500">{classItem.course}</p>
                        </div>
                        <div className="flex gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(classItem.type)}`}>
                            {classItem.type}
                          </span>
                          {classItem.canJoin && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                              Ready to Join
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar size={16} />
                          <span>{new Date(classItem.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock size={16} />
                          <span>{classItem.time}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin size={16} />
                          <span>{classItem.location}</span>
                        </div>
                      </div>

                      <p className="text-gray-600 mb-3">{classItem.description}</p>

                      {classItem.materials && classItem.materials.length > 0 && (
                        <div className="mb-3">
                          <p className="text-sm font-medium text-gray-700 mb-1">Materials:</p>
                          <div className="flex flex-wrap gap-2">
                            {classItem.materials.map((material, index) => (
                              <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                {material}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 lg:w-32">
                      {classItem.canJoin ? (
                        <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                          <Play size={16} />
                          Join Now
                        </button>
                      ) : (
                        <button 
                          disabled 
                          className="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg font-medium cursor-not-allowed flex items-center justify-center gap-2"
                        >
                          <Clock size={16} />
                          Scheduled
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}

        {activeTab === 'past' && (
          <>
            {filteredClasses(pastClasses).map((classItem) => (
              <div key={classItem.id} className="dashboard-card">
                <div className="dashboard-card-content">
                  <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-800 mb-1">{classItem.title}</h3>
                          <p className="text-gray-600">by {classItem.instructor}</p>
                          <p className="text-sm text-gray-500">{classItem.course}</p>
                        </div>
                        <div className="flex gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(classItem.type)}`}>
                            {classItem.type}
                          </span>
                          {classItem.attended ? (
                            <span className="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100 flex items-center gap-1">
                              <CheckCircle size={12} />
                              Attended
                            </span>
                          ) : (
                            <span className="px-2 py-1 rounded-full text-xs font-medium text-red-600 bg-red-100 flex items-center gap-1">
                              <AlertCircle size={12} />
                              Missed
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar size={16} />
                          <span>{new Date(classItem.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock size={16} />
                          <span>{classItem.time}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 lg:w-32">
                      {classItem.recording ? (
                        <button className="bg-music-background-orange hover:bg-music-background text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                          <Video size={16} />
                          Watch Recording
                        </button>
                      ) : (
                        <span className="text-gray-500 text-sm text-center">No recording</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>

      {/* Empty State */}
      {((activeTab === 'upcoming' && filteredClasses(upcomingClasses).length === 0) ||
        (activeTab === 'past' && filteredClasses(pastClasses).length === 0)) && (
        <div className="text-center py-12">
          <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">
            No {activeTab} classes found
          </h3>
          <p className="text-gray-500">
            {searchTerm || filterType !== 'all' 
              ? 'Try adjusting your search or filter criteria'
              : `You don't have any ${activeTab} classes at the moment`
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default Classes;
