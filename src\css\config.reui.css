@plugin 'tailwindcss-animate';

/** Colors **/
:root {
  /** Base Colors **/
  --background: oklch(1 0 0); /* --color-white */
  /* swaras academy colors */
  --music-background: #4F1800; /* --color-maroon */
  --music-background-light: #FAF5A0; /* --color-dark */
  --music-light: #ececec; /* --color-light */
  --music-background-orange: #B7742A; /* --color-dark */
  /* end swaras academy colors */


  
  --foreground: oklch(27.4% 0.006 286.033); /* --color-zinc-800 */
  --card: oklch(1 0 0); /* --color-white */
  --card-foreground: oklch(27.4% 0.006 286.033); /* --color-zinc-800 */
  --popover: oklch(1 0 0); /* --color-white */
  --popover-foreground: oklch(27.4% 0.006 286.033); /* --color-zinc-800 */
  --primary: #1379f0; /* custom color  */
  --primary-foreground: oklch(1 0 0); /* --color-white */
  --secondary: oklch(96.7% 0.003 264.542); /* --color-zinc-100 */
  --secondary-foreground: oklch(44.6% 0.03 256.802); /* --color-zinc-600 */
  --muted: oklch(96.7% 0.003 264.542); /* --color-zinc-100 */
  --muted-foreground: oklch(70.5% 0.015 286.067); /* --color-zinc-400 */
  --accent: oklch(96.7% 0.003 264.542); /* --color-zinc-100 */
  --accent-foreground: oklch(21% 0.006 285.885); /* --color-zinc-900 */
  --destructive: oklch(57.7% 0.245 27.325); /* --color-red-600 */
  --destructive-foreground: oklch(1 0 0); /* --color-white */
  --mono: oklch(14.1% 0.005 285.823); /* --color-zinc-950 */
  --mono-foreground: oklch(1 0 0); /* --color-white */

  /** Base Styles **/
  --border: oklch(
    94% 0.004 286.32
  ); /* between --color-zinc-100 and --color-zinc-200 */
  --input: oklch(92% 0.004 286.32); /* --color-zinc-200 */
  --ring: oklch(87.1% 0.006 286.286); /* --color-zinc-400 */
  --radius: 0.5rem;
}

.dark {
  /** Base Colors **/
  --background: oklch(14.1% 0.005 285.823); /* --color-zinc-950 */
  --foreground: oklch(98.5% 0 0); /* --color-zinc-50 */
  --card: oklch(14.1% 0.005 285.823); /* --color-zinc-950 */
  --card-foreground: oklch(98.5% 0 0); /* --color-zinc-50 */
  --popover: oklch(14.1% 0.005 285.823); /* --color-zinc-950 */
  --popover-foreground: oklch(98.5% 0 0); /* --color-zinc-50 */
  --primary: #1379f0; /* custom color  */
  --primary-foreground: oklch(1 0 0); /* --color-white */
  --secondary: oklch(27.4% 0.006 286.033); /* --color-zinc-800 */
  --secondary-foreground: oklch(70.5% 0.015 286.067); /* --color-zinc-400 */
  --muted: oklch(21% 0.006 285.885); /* --color-zinc-900 */
  --muted-foreground: oklch(55.2% 0.016 285.938); /* --color-zinc-500 */
  --accent: oklch(21% 0.006 285.885); /* --color-zinc-900 */
  --accent-foreground: oklch(98.5% 0 0); /* --color-zinc-50 */
  --destructive: oklch(57.7% 0.245 27.325); /* --color-red-600 */
  --destructive-foreground: oklch(1 0 0); /* --color-white */
  --mono: oklch(87.1% 0.006 286.286); /* --color-zinc-300 */
  --mono-foreground: oklch(0 0 0); /* --color-black */

  /** Base Styles **/
  --border: oklch(27.4% 0.006 286.033); /* --color-zinc-800 */
  --input: oklch(27.4% 0.006 286.033); /* --color-zinc-800 */
  --ring: oklch(27.4% 0.006 286.033); /* --color-zinc-600 */
}

/** Theme Setup **/
@theme inline {
  /** Base Colors **/
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  
  /* music colors */
  --color-music-background: var(--music-background);
  --color-music-background-light: var(--music-background-light);
  --color-music-light: var(--music-light);
  --color-music-background-orange: var(--music-background-orange);
  /* end music colors */

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-mono: var(--mono);
  --color-mono-foreground: var(--mono-foreground);

  /** Base Styles **/
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --radius-xl: calc(var(--radius) + 4px);
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-collapsible-down: collapsible-down 0.2s ease-out;
  --animate-collapsible-up: collapsible-up 0.2s ease-out;
  --animate-caret-blink: caret-blink 1.25s ease-out infinite;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes caret-blink {
    0%,
    70%,
    100% {
      opacity: 1;
    }
    20%,
    50% {
      opacity: 0;
    }
  }
}

/** Custom Font Size **/
@theme {
  --text-2sm: 0.8125rem;
  --text-2sm--line-height: calc(1.075 / 0.8125);

  --text-2xs: 0.6875rem;
  --text-2xs--line-height: calc(0.825 / 0.6875);
}
/** Custom Font Family **/