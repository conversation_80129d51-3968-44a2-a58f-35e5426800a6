import { useState } from "react";
import { Camera } from "lucide-react";
import blur_bg from "../../assets/background.png";
const StudentRegistrationForm = () => {
  // State for Date of Birth and Age
  const [dob, setDob] = useState("");
  const [age, setAge] = useState("");

  const handleDobChange = (e) => {
    const value = e.target.value;
    setDob(value);
    setAge(value ? calculateAge(value) : "");
  };

  function calculateAge(dob) {
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  }

  const handleSubmit = (e) => {
    e.preventDefault();
    // Form submission logic here
  };
  const CountryCode = [
    { code: "+91", name: "IN" },
    { code: "+971", name: "Dubai" },
    { code: "+1", name: "US" },
    { code: "+44", name: "UK" },
    { code: "+61", name: "AU" },
  ];

  const selectCountry = [
    {name: "India", code: "+91"},
    {name: "Dubai", code: "+971"},
    {name: "US", code: "+1"},
    {name: "UK", code: "+44"},
    {name: "AU", code: "+61"},
  ]

  return (
    <div
      className="min-h-screen bg-gray-50 py-8 px-4 bg-cover bg-center"
      style={{ backgroundImage: `url(${blur_bg})` }}
    >
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200 text-center">
            <h2 className="text-2xl font-semibold text-gray-900">
              Update Your Profile to Access Dashboard
            </h2>
          </div>

          <div className="max-h-[84vh] overflow-y-scroll sidebar-scroll">
            <form onSubmit={handleSubmit} className="p-8 space-y-6">
              {/* Photo Upload */}
              <div className="flex flex-col items-center gap-3">
                <div className="relative w-26 h-24">
                  <img
                    src=""
                    alt=""
                    className="w-26 h-26 object-cover  rounded-full border border-gray-300"
                  />
                  <label className="absolute bottom-0 right-0 w-7 h-7 bg-music-background-orange rounded-full flex items-center justify-center cursor-pointer hover:bg-music-background transition-colors">
                    <Camera size={20} className="text-white" />
                    <input type="file" accept="image/*" className="hidden" />
                  </label>
                </div>
                <span className="text-sm text-music-background font-medium">
                  Change Picture
                </span>
              </div>

              {/* Student ID */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Student ID
                </label>
                <input
                  type="text"
                  value={50897}
                  readOnly
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md bg-gray-100"
                />
              </div>

              {/* Full Name */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Full Name
                </label>
                <input
                  type="text"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Gender */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Gender
                </label>
                <select className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md">
                  <option value="">Select</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              {/* Phone Numbers */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Phone Number
                </label>
                <select className=" px-3 w-1/8 py-2.5 border border-gray-300 rounded-md">
                  {CountryCode.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.code} ({country.name})
                    </option>
                  ))}
                </select>
                <input
                  type="tel"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  WhatsApp Number
                </label>
                <input
                  type="tel"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Email */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Email
                </label>
                <input
                  type="email"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Address */}
              <div className="flex items-start gap-8">
                <label className="text-sm font-medium text-gray-700 w-80 pt-2">
                  Address
                </label>
                <textarea
                  rows="3"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                ></textarea>
              </div>

              {/* City */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  City
                </label>
                <input
                  type="text"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Country */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Country
                </label>
                <select className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md">
                  <option value="">Select Country</option>
                  {selectCountry.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* DOB + Age */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Date of Birth
                </label>
                <input
                  type="date"
                  id="dob"
                  value={dob}
                  onChange={handleDobChange}
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Age
                </label>
                <input
                  type="text"
                  id="age"
                  value={age}
                  readOnly
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md bg-gray-100"
                />
              </div>

              {/* Learning Since */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Learning Since
                </label>
                <input
                  type="text"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Guardian Info */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Guardian Name
                </label>
                <input
                  type="text"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Guardian Contact
                </label>
                <input
                  type="tel"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Timezone */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Timezone
                </label>
                <select className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md">
                  <option value="">Select Timezone</option>
                  <option value="Asia/Kolkata">Asia/Kolkata (GMT+5:30)</option>
                  <option value="America/New_York">
                    America/New York (GMT-5)
                  </option>
                  <option value="Europe/London">Europe/London (GMT+0)</option>
                </select>
              </div>

              {/* classtime */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                 Preferred Class Time
                </label>
                <select className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md">
                  <option value="">Select Time</option>
                  <option value="6 AM - 7 AM">6 AM - 7 AM</option>
                  <option value="8 AM - 9 AM">8 AM - 9 AM</option>
                  <option value="6 PM - 7 PM">6 PM - 7 PM</option>
                </select>
              </div>

              {/* Referral */}
              <div className="flex items-center gap-8">
                <label className="text-sm font-medium text-gray-700 w-80">
                  Heard About Us
                </label>
                <select className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md">
                  <option>Social Media</option>
                  <option>Word of Mouth</option>
                  <option>ISKCON Temple</option>
                  <option>Website</option>
                  <option>Other</option>
                </select>
              </div>

              {/* Musical Instrument */}
              <div className="flex items-start gap-8">
                <label className="text-sm font-medium text-gray-700 w-80 pt-2">
                  Do You Have a Musical Instrument?
                </label>
                <input
                  type="text"
                  placeholder="Yes / No - If yes, specify"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Previous Training */}
              <div className="flex items-start gap-8">
                <label className="text-sm font-medium text-gray-700 w-80 pt-2">
                  Have You Taken Any Previous Music Training
                </label>
                <input
                  type="text"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* ISKCON Association */}
              <div className="flex items-start gap-8">
                <label className="text-sm font-medium text-gray-700 w-80 pt-2">
                  ISKCON Association Duration
                </label>
                <input
                  type="text"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                />
              </div>

              {/* Motivation */}
              <div className="flex items-start gap-8">
                <label className="text-sm font-medium text-gray-700 w-80 pt-2">
                  Why Do You Want To Learn Music?
                </label>
                <textarea
                  rows="3"
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md"
                ></textarea>
              </div>

              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="reset"
                  className="cursor-pointer font-semibold px-6 py-2.5 text-music-background bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Reset
                </button>
                <button
                  type="submit"
                  className="cursor-pointer font-semibold px-6 py-2.5 bg-music-background-orange hover:bg-music-background text-white rounded-md"
                >
                  Save Changes
                </button>
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentRegistrationForm;




