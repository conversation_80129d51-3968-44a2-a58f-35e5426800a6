import { useState } from 'react';
import { 
  Video, 
  Play, 
  Clock, 
  Calendar, 
  Download,
  Search,
  Filter,
  BookOpen,
  Star,
  Eye,
  ChevronDown
} from 'lucide-react';

const ClassRecordings = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [selectedInstructor, setSelectedInstructor] = useState('all');
  const [sortBy, setSortBy] = useState('date-desc');

  const recordings = [
    {
      id: 1,
      title: 'Tabla Fundamentals - Hand Positions',
      instructor: '<PERSON>',
      course: 'Tabla Fundamentals',
      date: '2024-12-15',
      duration: '58:32',
      views: 45,
      rating: 4.8,
      thumbnail: '/api/placeholder/320/180',
      description: 'Learn proper hand positions and basic striking techniques for tabla',
      tags: ['Beginner', 'Technique', 'Fundamentals'],
      downloadable: true,
      quality: 'HD'
    },
    {
      id: 2,
      title: 'Vocal Warm-up Techniques',
      instructor: 'Sm<PERSON>. <PERSON><PERSON>',
      course: 'Classical Vocal Training',
      date: '2024-12-14',
      duration: '45:20',
      views: 32,
      rating: 4.9,
      thumbnail: '/api/placeholder/320/180',
      description: 'Essential warm-up exercises for classical vocal training',
      tags: ['Vocal', 'Warm-up', 'Breathing'],
      downloadable: true,
      quality: 'HD'
    },
    {
      id: 3,
      title: 'Sitar Tuning Workshop',
      instructor: 'Pt. Ravi Shankar',
      course: 'Sitar Intermediate',
      date: '2024-12-13',
      duration: '1:12:45',
      views: 28,
      rating: 4.7,
      thumbnail: '/api/placeholder/320/180',
      description: 'Complete guide to tuning your sitar properly',
      tags: ['Sitar', 'Tuning', 'Maintenance'],
      downloadable: false,
      quality: 'HD'
    },
    {
      id: 4,
      title: 'Rhythm Patterns in Tabla',
      instructor: 'Guru Ramesh Kumar',
      course: 'Tabla Fundamentals',
      date: '2024-12-12',
      duration: '52:18',
      views: 67,
      rating: 4.6,
      thumbnail: '/api/placeholder/320/180',
      description: 'Explore basic rhythm patterns and their variations',
      tags: ['Rhythm', 'Patterns', 'Practice'],
      downloadable: true,
      quality: 'HD'
    },
    {
      id: 5,
      title: 'Classical Ragas Introduction',
      instructor: 'Smt. Priya Sharma',
      course: 'Classical Vocal Training',
      date: '2024-12-11',
      duration: '1:05:30',
      views: 41,
      rating: 4.8,
      thumbnail: '/api/placeholder/320/180',
      description: 'Introduction to major classical ragas and their characteristics',
      tags: ['Ragas', 'Theory', 'Classical'],
      downloadable: true,
      quality: 'HD'
    },
    {
      id: 6,
      title: 'Sitar Finger Exercises',
      instructor: 'Pt. Ravi Shankar',
      course: 'Sitar Intermediate',
      date: '2024-12-10',
      duration: '38:45',
      views: 35,
      rating: 4.5,
      thumbnail: '/api/placeholder/320/180',
      description: 'Essential finger exercises for sitar players',
      tags: ['Exercises', 'Technique', 'Finger Work'],
      downloadable: true,
      quality: 'HD'
    }
  ];

  const courses = [...new Set(recordings.map(r => r.course))];
  const instructors = [...new Set(recordings.map(r => r.instructor))];

  const filteredRecordings = recordings.filter(recording => {
    const matchesSearch = recording.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         recording.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         recording.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         recording.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCourse = selectedCourse === 'all' || recording.course === selectedCourse;
    const matchesInstructor = selectedInstructor === 'all' || recording.instructor === selectedInstructor;
    
    return matchesSearch && matchesCourse && matchesInstructor;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'date-desc':
        return new Date(b.date) - new Date(a.date);
      case 'date-asc':
        return new Date(a.date) - new Date(b.date);
      case 'views-desc':
        return b.views - a.views;
      case 'rating-desc':
        return b.rating - a.rating;
      case 'duration-desc':
        return b.duration.localeCompare(a.duration);
      default:
        return 0;
    }
  });

  const formatDuration = (duration) => {
    return duration;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="dashboard-section">
      {/* Page Header */}
      <div className="page-header mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Class Recordings</h1>
        <p className="text-gray-600 text-xl">Access recordings of your previous classes and lessons</p>
      </div>

      {/* Search and Filters */}
      <div className="dashboard-card mb-6">
        <div className="dashboard-card-content">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="lg:col-span-2 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search recordings, instructors, or topics..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent"
              />
            </div>

            {/* Course Filter */}
            <div className="relative">
              <select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Courses</option>
                {courses.map(course => (
                  <option key={course} value={course}>{course}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>

            {/* Instructor Filter */}
            <div className="relative">
              <select
                value={selectedInstructor}
                onChange={(e) => setSelectedInstructor(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Instructors</option>
                {instructors.map(instructor => (
                  <option key={instructor} value={instructor}>{instructor}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>

            {/* Sort */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-music-background-orange focus:border-transparent appearance-none bg-white"
              >
                <option value="date-desc">Newest First</option>
                <option value="date-asc">Oldest First</option>
                <option value="views-desc">Most Viewed</option>
                <option value="rating-desc">Highest Rated</option>
                <option value="duration-desc">Longest First</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">
          Showing {filteredRecordings.length} recording{filteredRecordings.length !== 1 ? 's' : ''}
          {searchTerm && ` for "${searchTerm}"`}
        </p>
      </div>

      {/* Recordings Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRecordings.map((recording) => (
          <div key={recording.id} className="dashboard-card group hover:shadow-lg transition-shadow">
            {/* Thumbnail */}
            <div className="relative">
              <div className="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-music-background-orange/20 to-music-background/20 flex items-center justify-center">
                  <Video size={48} className="text-music-background-orange" />
                </div>
              </div>
              
              {/* Play Button Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center rounded-t-xl">
                <button className="bg-white bg-opacity-0 group-hover:bg-opacity-90 text-music-background-orange p-3 rounded-full transition-all duration-200 transform scale-0 group-hover:scale-100">
                  <Play size={24} fill="currentColor" />
                </button>
              </div>

              {/* Duration Badge */}
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                {formatDuration(recording.duration)}
              </div>

              {/* Quality Badge */}
              <div className="absolute top-2 left-2 bg-music-background-orange text-white text-xs px-2 py-1 rounded">
                {recording.quality}
              </div>
            </div>

            {/* Content */}
            <div className="dashboard-card-content">
              <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2">{recording.title}</h3>
              
              <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                <BookOpen size={14} />
                <span>{recording.course}</span>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">by {recording.instructor}</p>
              
              <p className="text-sm text-gray-500 mb-3 line-clamp-2">{recording.description}</p>

              {/* Tags */}
              <div className="flex flex-wrap gap-1 mb-3">
                {recording.tags.slice(0, 3).map((tag, index) => (
                  <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {tag}
                  </span>
                ))}
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Calendar size={14} />
                    <span>{formatDate(recording.date)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye size={14} />
                    <span>{recording.views}</span>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Star size={14} className="text-yellow-500" fill="currentColor" />
                  <span>{recording.rating}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <button className="flex-1 bg-music-background-orange hover:bg-music-background text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                  <Play size={16} />
                  Watch
                </button>
                {recording.downloadable && (
                  <button className="px-3 py-2 border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors">
                    <Download size={16} />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredRecordings.length === 0 && (
        <div className="text-center py-12">
          <Video size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No recordings found</h3>
          <p className="text-gray-500">
            {searchTerm || selectedCourse !== 'all' || selectedInstructor !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'No class recordings are available at the moment'
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default ClassRecordings;
